using System.Collections.Generic;
using UnityEngine;
using UnityEngine.InputSystem;
using Sirenix.OdinInspector;
using System.Collections;

[System.Serializable]
public class SpellEchoData
{
    public int slotIndex;
    public int remainingEchoes;
    // Removed targetPosition - now uses current mouse position for each echo
    public GemSocketController controller;
    public SkillGemData skillData;
    public float echoDelay;
    public float echoSpreadRadius;
}

public class SkillExecutor : MonoBehaviour
{
    [Title("References")]
    [SerializeField]
    private EquipmentPanel equipmentPanel;
    
    [SerializeField]
    private PlayerStats playerStats;
    
    [Title("Skill State")]
    [ShowInInspector, ReadOnly]
    private Dictionary<int, float> skillCooldowns = new Dictionary<int, float>();
    
    [ShowInInspector, ReadOnly]
    private Dictionary<int, GemSocketController> cachedControllers = new Dictionary<int, GemSocketController>();
    
    [Title("Spell Echo State")]
    [ShowInInspector, ReadOnly]
    private List<Coroutine> activeSpellEchoes = new List<Coroutine>();

    [Header("Debug Settings")]
    [SerializeField] private bool enableDebugLogging = false;
    [Tooltip("Enable debug logging for skill execution events")]

    // Pooled collection to reduce GC pressure in Update()
    private readonly List<int> _reusableKeysList = new List<int>();

    // Cached vectors to eliminate frequent allocations
    private Vector2 _cachedMouseScreenPos;
    private Vector3 _cachedMouseWorldPos;
    private Vector3 _cachedVector3Temp;
    private Vector2 _cachedVector2Temp;
    private Vector2 _cachedDirectionVector;
    private Vector2 _cachedOffsetVector;
    private Vector2 _cachedPerpendicularVector;

    // Cached support gem collections to reduce method call overhead
    private readonly Dictionary<int, List<GemInstance>> _cachedSupportGems = new Dictionary<int, List<GemInstance>>();

    // Mouse position caching to avoid repeated ScreenToWorldPoint calls
    private Vector3 _lastCachedMousePosition;
    private int _mousePositionCacheFrame = -1;

    // Execution-scoped cache to eliminate repeated GemSocketController calculations
    private struct SkillExecutionCache
    {
        public float finalDamage;
        public float finalCritChance;
        public float finalCritMultiplier;
        public bool hasPierce;
        public bool hasChain;
        public bool hasFork;
        public bool hasAreaDamage;
        public int chainCount;
        public int forkCount;
        public float forkAngle;
        public float areaRadius;
        public float playerModifiedDamage;
        public bool isValid;
        public int slotIndex;
    }

    private SkillExecutionCache _executionCache;

    // PlayerStats calculation caching
    private float _cachedPlayerDamageIncreased;
    private float _cachedPlayerDamageMultiplier;
    private int _playerStatsCacheFrame = -1;

    private Camera mainCamera;
    
    private void Awake()
    {
        if (equipmentPanel == null)
            equipmentPanel = FindFirstObjectByType<EquipmentPanel>();
            
        if (playerStats == null)
            playerStats = GetComponent<PlayerStats>();
            
        mainCamera = Camera.main;
    }

    private void Start(){
        if(playerStats == null){
            playerStats = PlayerManager.PlayerStats;
        }
    }
    
    private void Update()
    {
        // Update cooldowns using pooled list to reduce GC pressure
        _reusableKeysList.Clear();
        _reusableKeysList.AddRange(skillCooldowns.Keys);
        foreach (var key in _reusableKeysList)
        {
            if (skillCooldowns[key] > 0)
            {
                skillCooldowns[key] -= Time.deltaTime;
                if (skillCooldowns[key] <= 0)
                {
                    skillCooldowns.Remove(key);
                }
            }
        }
    }
    
    public bool TryExecuteSkill(int slotIndex)
    {
        if (equipmentPanel == null) return false;
        
        // Check cooldown
        if (skillCooldowns.ContainsKey(slotIndex) && skillCooldowns[slotIndex] > 0)
        {
            if (enableDebugLogging)
            {
                Debug.Log("Skill in slot " + slotIndex + " is on cooldown: " + skillCooldowns[slotIndex].ToString("F1") + "s remaining");
            }
            return false;
        }
        
        // Get or cache the controller
        if (!cachedControllers.TryGetValue(slotIndex, out var controller) || controller == null || controller.skillGemInstance == null)
        {
            controller = equipmentPanel.GetActiveSkillController(slotIndex);
            if (controller == null || controller.skillGemInstance == null)
            {
                if (enableDebugLogging)
                {
                    Debug.Log("No skill gem equipped in slot " + slotIndex);
                }
                return false;
            }
            cachedControllers[slotIndex] = controller;
        }
        
        var skillData = controller.skillGemInstance.gemDataTemplate as SkillGemData;
        if (skillData == null || skillData.skillPrefab == null)
        {
            Debug.LogError($"Skill gem in slot {slotIndex} has no prefab assigned!");
            return false;
        }
        
        // Check mana cost
        float manaCost = controller.CalculateFinalManaCost();
        if (playerStats.currentMana < manaCost)
        {
            if (enableDebugLogging)
            {
                Debug.Log("Not enough mana. Required: " + manaCost + ", Current: " + playerStats.currentMana);
            }
            return false;
        }

        // Get cached mouse position for targeting
        Vector3 mousePosition = GetCachedMouseWorldPosition();
        
        // Execute based on skill type
        switch (skillData.skillType)
        {
            case SkillType.Instant:
                ExecuteInstantSkill(controller, skillData, mousePosition, slotIndex);
                break;

            case SkillType.Projectile:
                ExecuteProjectileSkill(controller, skillData, mousePosition, slotIndex);
                break;
        }
        
        // Consume mana
        playerStats.SpendMana(manaCost);
        
        // Set cooldown (apply attack speed multiplier from gems and player stats)
        float cooldown = controller.CalculateFinalCooldown();
        float gemAttackSpeed = controller.CalculateFinalAttackSpeed();
        float playerAttackSpeed = playerStats?.GetCalculatedStat(StatType.AttackSpeed) ?? 1f;
        
        // Combine gem and player attack speed multipliers
        float totalAttackSpeed = gemAttackSpeed * playerAttackSpeed;
        
        if (totalAttackSpeed > 0)
        {
            cooldown /= totalAttackSpeed;
        }
        if (cooldown > 0)
        {
            skillCooldowns[slotIndex] = cooldown;
        }
        
        // Check for spell echo support gems using cached support gems
        CheckAndStartSpellEcho(slotIndex, controller, skillData);
        
        return true;
    }
    
    private void ExecuteInstantSkill(GemSocketController controller, SkillGemData skillData, Vector3 targetPosition, int slotIndex)
    {
        // Pre-cache all expensive calculations to eliminate repeated allocations
        CacheSkillExecutionValues(controller, slotIndex);

        Vector3 baseSpawnPosition = skillData.targetGroundPosition ? targetPosition : transform.position;

        // Get spell count from support gems (multi shot)
        int spellCount = controller.GetTotalProjectileCount();
        
        // Calculate spread radius for multiple instant spells
        float spreadRadius = 0f;
        if (spellCount > 1)
        {
            // Spread radius increases with spell count (1.5 units per extra spell)
            spreadRadius = (spellCount - 1) * 1.5f;
        }
        
        // Spawn multiple instant spells
        for (int i = 0; i < spellCount; i++)
        {
            Vector3 spawnPosition = baseSpawnPosition;
            
            // For multiple spells, spread them in a circle around the target
            if (spellCount > 1)
            {
                float angle = (360f / spellCount) * i * Mathf.Deg2Rad;
                _cachedOffsetVector.Set(Mathf.Cos(angle), Mathf.Sin(angle));
                _cachedOffsetVector *= spreadRadius;
                spawnPosition += (Vector3)_cachedOffsetVector;
            }
            
            GameObject skillObject = PoolManager.Instance.Spawn(skillData.skillPrefab, spawnPosition, Quaternion.identity);
            if (skillObject == null) continue;
            
            // Check if this is an InstantSpell component
            if (PoolManager.Instance.GetCachedComponent<InstantSpell>(skillObject, out var instantSpell))
            {
                CollisionLayers layer = CollisionLayers.PlayerProjectile; // Assuming player skills

                // Use cached crit stats
                instantSpell.critChance = _executionCache.finalCritChance;
                instantSpell.critMultiplier = _executionCache.finalCritMultiplier;
                instantSpell.damageType = skillData.damageType;
                instantSpell.ailmentChance = skillData.ailmentChance;

                // Pass gem data for status effect configuration
                instantSpell.skillGemData = skillData;
                instantSpell.supportGems = GetCachedSupportGems(controller, slotIndex);

                // Pass actual damage value, not multiplier
                instantSpell.Initialize((Vector2)spawnPosition, _executionCache.playerModifiedDamage, layer);
            }
            else
            {
                // Fallback for other instant skill types
                ApplySkillEffects(skillObject, controller);
                
                // Handle area damage if supported using cached values
                if (_executionCache.hasAreaDamage)
                {
                    ApplyAreaDamage(spawnPosition, _executionCache.playerModifiedDamage, _executionCache.areaRadius);
                }
            }
        }
    }
    
    private void ExecuteProjectileSkill(GemSocketController controller, SkillGemData skillData, Vector3 targetPosition, int slotIndex)
    {
        // Pre-cache all expensive calculations to eliminate repeated allocations
        CacheSkillExecutionValues(controller, slotIndex);

        _cachedDirectionVector = (targetPosition - transform.position).normalized;
        float baseAngle = Mathf.Atan2(_cachedDirectionVector.y, _cachedDirectionVector.x) * Mathf.Rad2Deg;

        // Get projectile count and spread (these are relatively cheap)
        int projectileCount = controller.GetTotalProjectileCount();
        bool useParallel = controller.UseParallelProjectiles();
        
        // Spawn multiple projectiles
        for (int i = 0; i < projectileCount; i++)
        {
            Vector3 spawnPosition;
            Quaternion rotation;
            Vector2 direction;

            if (useParallel)
            {
                // Parallel projectiles - calculate lateral offset
                float lateralOffset = controller.GetProjectileLateralOffset();
                float totalWidth = (projectileCount - 1) * lateralOffset;
                float offsetAmount = -totalWidth / 2f + (i * lateralOffset);

                // Calculate perpendicular vector for offset using cached vector
                _cachedPerpendicularVector.Set(-_cachedDirectionVector.y, _cachedDirectionVector.x);
                _cachedPerpendicularVector *= offsetAmount;
                spawnPosition = transform.position + (Vector3)_cachedPerpendicularVector;

                // All parallel projectiles use the same angle and direction
                rotation = Quaternion.Euler(0, 0, baseAngle);
                direction = _cachedDirectionVector;
            }
            else
            {
                // Angular spread (original behavior)
                float spreadAngle = controller.GetProjectileSpreadAngle();
                float angleStep = projectileCount > 1 ? spreadAngle : 0;
                float startAngle = baseAngle - (angleStep * (projectileCount - 1) / 2f);
                float currentAngle = startAngle + (angleStep * i);

                rotation = Quaternion.Euler(0, 0, currentAngle);
                float radians = currentAngle * Mathf.Deg2Rad;
                // Use cached vector for direction calculation
                _cachedVector2Temp.Set(Mathf.Cos(radians), Mathf.Sin(radians));
                direction = _cachedVector2Temp;
                spawnPosition = transform.position;
            }
            
            GameObject projectileObj = PoolManager.Instance.Spawn(skillData.skillPrefab, spawnPosition, rotation);
            if (projectileObj == null) continue;
            
            // Configure projectile using cached values
            if (PoolManager.Instance.GetCachedComponent<Projectile>(projectileObj, out var projectile))
            {
                // Use cached values instead of expensive method calls
                projectile.damage = _executionCache.playerModifiedDamage;
                projectile.speed = skillData.projectileSpeed;
                projectile.lifetime = skillData.duration;
                projectile.critChance = _executionCache.finalCritChance;
                projectile.critMultiplier = _executionCache.finalCritMultiplier;
                projectile.damageType = skillData.damageType;
                projectile.ailmentChance = skillData.ailmentChance;

                // Pass gem data for status effect configuration
                projectile.skillGemData = skillData;
                projectile.supportGems = GetCachedSupportGems(controller, slotIndex);

                // Apply support gem effects using cached values
                if (_executionCache.hasPierce)
                {
                    projectile.SetPiercing(true);
                }

                if (_executionCache.hasChain)
                {
                    projectile.SetChaining(true, _executionCache.chainCount);
                }

                if (_executionCache.hasFork)
                {
                    projectile.SetFork(true, _executionCache.forkCount, _executionCache.forkAngle);
                }

                if (_executionCache.hasAreaDamage)
                {
                    projectile.SetAreaDamage(true, _executionCache.areaRadius);
                }

                // Initialize projectile with cached damage (pass actual damage value, not multiplier)
                CollisionLayers layer = CollisionLayers.PlayerProjectile; // Assuming player skills
                projectile.Initialize((Vector2)spawnPosition, direction, _executionCache.playerModifiedDamage, layer, skillData.projectileSpeed, skillData.duration);
            }
        }
    }
    
    private void ApplySkillEffects(GameObject skillObject, GemSocketController controller)
    {
        // Apply damage multipliers and effects to the skill object
        // This would be customized based on your skill system

        // Use cached damage calculation (assumes CacheSkillExecutionValues was called)
        float totalDamage = _executionCache.isValid ? _executionCache.playerModifiedDamage :
                           GetCachedPlayerDamageModifiers(controller.CalculateFinalDamage(), controller);

        // Look for damage dealers on the skill object and its children
        // Note: IDamageDealer is an interface, so we can't use PoolManager's GetCachedComponent
        // which requires Component types. We'll use the standard approach but cache the result.
        var damageDealers = skillObject.GetComponentsInChildren<IDamageDealer>();
        foreach (var dealer in damageDealers)
        {
            dealer.SetDamage(totalDamage);
        }
    }
    
    private void ApplyAreaDamage(Vector3 center, float damage, float radius)
    {
        // Use custom collision system to find enemies in radius
        if (CollisionManager.Instance == null) return;
        
        var collidables = CollisionManager.Instance.GetCollidersInRadius(
            center, radius, CollisionLayers.Enemy);
            
        foreach (var collidable in collidables)
        {
            var target = collidable.GameObject;
            
            // Apply damage using the same system as projectiles
            if (PlayerManager.PlayerGameObject != null && target == PlayerManager.PlayerGameObject)
            {
                PlayerManager.DealDamageToPlayer(new DamageInfo(damage, DamageType.Physical));
            }
            else if (PoolManager.Instance != null && PoolManager.Instance.GetCachedComponent<HealthComponent>(target, out var healthComponent))
            {
                healthComponent.TakeDamage(damage);
            }
            else if (target.TryGetComponent<HealthComponent>(out var directHealth))
            {
                directHealth.TakeDamage(damage);
            }
            // Fallback to IDamageable for compatibility
            else if (target.TryGetComponent<IDamageable>(out var damageable))
            {
                damageable.TakeDamage(damage);
            }
        }
    }
    
    public void InvalidateCache(int slotIndex)
    {
        cachedControllers.Remove(slotIndex);
        _cachedSupportGems.Remove(slotIndex);

        // Invalidate execution cache if it's for this slot
        if (_executionCache.slotIndex == slotIndex)
        {
            _executionCache.isValid = false;
        }
    }

    public void InvalidateAllCaches()
    {
        cachedControllers.Clear();
        _cachedSupportGems.Clear();
        _mousePositionCacheFrame = -1;
        _playerStatsCacheFrame = -1;
        _executionCache.isValid = false;
    }

    /// <summary>
    /// Gets cached mouse world position, avoiding repeated ScreenToWorldPoint calls within the same frame
    /// </summary>
    private Vector3 GetCachedMouseWorldPosition()
    {
        int currentFrame = Time.frameCount;
        if (_mousePositionCacheFrame != currentFrame)
        {
            _cachedMouseScreenPos = Mouse.current.position.ReadValue();
            _cachedVector3Temp.Set(_cachedMouseScreenPos.x, _cachedMouseScreenPos.y, mainCamera.nearClipPlane);
            _lastCachedMousePosition = mainCamera.ScreenToWorldPoint(_cachedVector3Temp);
            _lastCachedMousePosition.z = 0;
            _mousePositionCacheFrame = currentFrame;
        }
        return _lastCachedMousePosition;
    }

    /// <summary>
    /// Gets cached support gems for a slot, reducing repeated method calls
    /// </summary>
    private List<GemInstance> GetCachedSupportGems(GemSocketController controller, int slotIndex)
    {
        if (!_cachedSupportGems.TryGetValue(slotIndex, out var supportGems) || supportGems == null)
        {
            supportGems = controller.GetCompatibleSupportGems();
            _cachedSupportGems[slotIndex] = supportGems;
        }
        return supportGems;
    }

    /// <summary>
    /// Pre-calculates all expensive GemSocketController values for the current skill execution
    /// </summary>
    private void CacheSkillExecutionValues(GemSocketController controller, int slotIndex)
    {
        if (_executionCache.isValid && _executionCache.slotIndex == slotIndex)
            return;

        // Cache all expensive calculations once
        _executionCache.finalDamage = controller.CalculateFinalDamage();
        _executionCache.finalCritChance = controller.CalculateFinalCritChance();
        _executionCache.finalCritMultiplier = controller.CalculateFinalCritMultiplier();
        _executionCache.hasPierce = controller.HasPierce();
        _executionCache.hasChain = controller.HasChain();
        _executionCache.hasFork = controller.HasFork();
        _executionCache.hasAreaDamage = controller.HasAreaDamage();
        _executionCache.chainCount = controller.GetChainCount();
        _executionCache.forkCount = controller.GetForkCount();
        _executionCache.forkAngle = controller.GetForkAngle();
        _executionCache.areaRadius = controller.GetAreaRadius();

        // Cache player damage modifiers
        _executionCache.playerModifiedDamage = GetCachedPlayerDamageModifiers(_executionCache.finalDamage, controller);

        _executionCache.isValid = true;
        _executionCache.slotIndex = slotIndex;
    }

    /// <summary>
    /// Gets cached player damage modifiers, avoiding repeated PlayerStats calculations
    /// </summary>
    private float GetCachedPlayerDamageModifiers(float baseDamage, GemSocketController controller)
    {
        if (playerStats == null) return baseDamage;

        // Cache PlayerStats calculations per frame
        int currentFrame = Time.frameCount;
        if (_playerStatsCacheFrame != currentFrame)
        {
            _cachedPlayerDamageIncreased = playerStats.GetCalculatedStat(StatType.DamageIncreased);
            _cachedPlayerDamageMultiplier = playerStats.GetCalculatedStat(StatType.DamageMultiplier);
            _playerStatsCacheFrame = currentFrame;
        }

        // Get support gem increased damage (additive with player increased)
        float supportGemIncreased = controller?.GetTotalIncreasedDamage() ?? 0f;

        // Combine all "increased" modifiers additively
        float totalDamageIncreased = _cachedPlayerDamageIncreased + supportGemIncreased;

        // Apply damage formula: baseDamage * (1 + totalIncreased/100) * damageMultiplier
        float damageWithIncreased = baseDamage * (1f + totalDamageIncreased / 100f);
        float finalDamage = damageWithIncreased * _cachedPlayerDamageMultiplier;

        return finalDamage;
    }
    
    public bool IsSkillOnCooldown(int slotIndex)
    {
        return skillCooldowns.ContainsKey(slotIndex) && skillCooldowns[slotIndex] > 0;
    }
    
    public float GetRemainingCooldown(int slotIndex)
    {
        return skillCooldowns.TryGetValue(slotIndex, out float cooldown) ? cooldown : 0f;
    }
    
    // Legacy method - now redirects to cached version for compatibility
    private float ApplyPlayerDamageModifiers(float baseDamage, GemSocketController controller = null)
    {
        return GetCachedPlayerDamageModifiers(baseDamage, controller);
    }
    
    private void CheckAndStartSpellEcho(int slotIndex, GemSocketController controller, SkillGemData skillData)
    {
        // Only check for spell echo if the skill has the Spell tag
        if ((skillData.gemTags & GemTag.Spell) == 0)
            return;
            
        // Check for spell echo support gems using cached collection
        var supportGems = GetCachedSupportGems(controller, slotIndex);
        foreach (var supportInstance in supportGems)
        {
            if (supportInstance?.gemDataTemplate is SupportGemData supportGem && supportGem.addsSpellEcho)
            {
                // Get echo count with rarity scaling
                int echoes = supportInstance.GetSpellEchoCount();
                
                // Create spell echo data
                var echoData = new SpellEchoData
                {
                    slotIndex = slotIndex,
                    remainingEchoes = echoes,
                    // No longer storing targetPosition - uses current mouse position for each echo
                    controller = controller,
                    skillData = skillData,
                    echoDelay = supportGem.echoDelay,
                    echoSpreadRadius = supportGem.echoSpreadRadius
                };
                
                // Start the echo coroutine
                var echoCoroutine = StartCoroutine(ProcessSpellEcho(echoData));
                activeSpellEchoes.Add(echoCoroutine);
                
                // Only apply the first spell echo gem found
                break;
            }
        }
    }
    
    private IEnumerator ProcessSpellEcho(SpellEchoData echoData)
    {
        while (echoData.remainingEchoes > 0)
        {
            // Wait for echo delay
            yield return new WaitForSeconds(echoData.echoDelay);

            // Check if the skill slot is still valid
            if (equipmentPanel == null || echoData.controller == null || echoData.skillData == null)
                break;

            // Get CURRENT mouse position for echo targeting using cached method
            Vector3 currentMousePosition = GetCachedMouseWorldPosition();

            if (enableDebugLogging)
            {
                Debug.Log("Spell Echo: Targeting current mouse position " + currentMousePosition);
            }

            // Calculate echo position with optional spread from CURRENT mouse position
            Vector3 echoPosition = currentMousePosition;
            if (echoData.echoSpreadRadius > 0)
            {
                _cachedVector2Temp = Random.insideUnitCircle * echoData.echoSpreadRadius;
                _cachedVector3Temp.Set(_cachedVector2Temp.x, _cachedVector2Temp.y, 0);
                echoPosition += _cachedVector3Temp;
            }

            // Execute the echo cast (free - no mana cost)
            ExecuteEchoSkill(echoData.controller, echoData.skillData, echoPosition, echoData.slotIndex);

            echoData.remainingEchoes--;
        }

        // Remove from active echoes list
        activeSpellEchoes.RemoveAll(c => c == null);
    }
    
    private void ExecuteEchoSkill(GemSocketController controller, SkillGemData skillData, Vector3 targetPosition, int slotIndex)
    {
        // Execute based on skill type (similar to normal execution but without mana/cooldown)
        switch (skillData.skillType)
        {
            case SkillType.Instant:
                ExecuteInstantSkill(controller, skillData, targetPosition, slotIndex);
                break;

            case SkillType.Projectile:
                ExecuteProjectileSkill(controller, skillData, targetPosition, slotIndex);
                break;
        }
        
        // Visual/audio feedback for echo cast
        if (ParticleEffectManager.Instance != null)
        {
            ParticleEffectManager.Instance.SpawnParticle(ParticleType.MagicAura, transform.position, 5);
        }
    }
}

// Interface for objects that can deal damage
public interface IDamageDealer
{
    void SetDamage(float damage);
}