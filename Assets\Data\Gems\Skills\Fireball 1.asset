%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc7afdaae02bac047acf72aabe6a2a9c, type: 3}
  m_Name: Fireball 1
  m_EditorClassIdentifier: Assembly-CSharp::RogueLike.Items.SkillGemData
  icon: {fileID: 7232748329181079804, guid: f04c2fc1d1a37ac4a88d614a36cf3b0b, type: 3}
  backgroundIcon: {fileID: 0}
  foregroundIcon: {fileID: 0}
  gemName: Fireball
  description: Fireball der test
  rarity: 2
  skillType: 1
  gemTags: 6
  skillPrefab: {fileID: 2340940985034927489, guid: 1465da85af5c3da44bf83918c692fc94, type: 3}
  baseDamage: 10
  cooldown: 0.1
  manaCost: 1
  projectileSpeed: 10
  duration: 0.75
  targetGroundPosition: 1
  attackSpeedMultiplier: 1
  critChance: 5
  critMultiplier: 2
  damageType: 1
  ailmentChance: 100
  ignitePercent: 0.2
  igniteDuration: 4
  freezeSlowAmount: 0.5
  freezeDuration: 2
  bleedPercent: 0.15
  bleedDuration: 6
  shockChainDamage: 0.1
  shockChainRange: 3
  shockDuration: 2
