using UnityEngine;
using Sirenix.OdinInspector;
using System.Collections.Generic;
using System.Collections;
using System.Linq;

public enum ChunkContentType
{
    None,
    Enemies,
    Boss,
    TreasureChest
}

/// <summary>
/// System that spawns various content types in chunks when first visited.
/// Content is despawned when player moves too far away.
/// </summary>
public class ChunkContentSpawner : MonoBehaviour
{
    [Title("References")]
    [Required]
    [SerializeField] private TilemapChunkManager chunkManager;
    
    [Required]
    [SerializeField] private Transform playerTransform;
    
    [Title("Content Prefabs")]
    [Required]
    [AssetsOnly]
    [SerializeField] private GameObject bossPrefab;
    
    [Title("Enemy Race System")]
    [InfoBox("Race-based enemy spawning system with weighted selection")]
    [Required]
    [AssetsOnly]
    [SerializeField] private ScriptableObject enemySpawnConfig;
    
    [Required]
    [AssetsOnly]
    [SerializeField] private GameObject chestPrefab;
    
    [Required]
    [SerializeField] private Camera mainCamera;
    
    [SerializeField]
    [Tooltip("Reference to ChunkBasedGraphMover to ensure pathfinding is ready")]
    private ChunkBasedGraphMover graphMover;
    
    [Title("Spawn Configuration")]
    [Header("Content Type Probabilities")]
    [Range(0f, 1f)]
    [SerializeField] private float enemySpawnChance = 0.7f;
    

    
    [Range(0f, 1f)]
    [SerializeField] private float bossSpawnChance = 0.1f;
    
    [Range(0f, 1f)]
    [SerializeField] private float chestSpawnChance = 0.2f;
    
    [Header("Enemy Settings")]
    [MinValue(1)]
    [SerializeField] private int minEnemiesPerChunk = 3;
    
    [MinValue(1)]
    [SerializeField] private int maxEnemiesPerChunk = 5;
    
    [Header("Special Content Settings")]
    [MinValue(2)]
    [Tooltip("Minimum chunks between boss spawns")]
    [SerializeField] private int minChunksBetweenBosses = 3;
    
    [MinValue(1)]
    [Tooltip("Minimum chunks between chest spawns")]
    [SerializeField] private int minChunksBetweenChests = 2;
    
    [MinValue(0.1f)]
    [SerializeField] private float spawnMargin = 1f;
    
    [Title("Update Settings")]
    [MinValue(0.1f)]
    [SerializeField] private float updateInterval = 0.5f;
    
    [Header("Debug Settings")]
    [SerializeField] private bool showDebugInfo = false;
    [Tooltip("Enable debug logging for enemy spawning and chunk content operations")]
    
    // Runtime data
    private HashSet<ChunkCoordinate> visitedChunks = new HashSet<ChunkCoordinate>();
    private Dictionary<ChunkCoordinate, List<GameObject>> spawnedContentPerChunk = new Dictionary<ChunkCoordinate, List<GameObject>>();
    private Dictionary<ChunkCoordinate, ChunkContentType> chunkContentTypes = new Dictionary<ChunkCoordinate, ChunkContentType>();
    private ChunkCoordinate currentPlayerChunk;
    private ChunkCoordinate startingChunk;
    private ChunkCoordinate lastBossChunk;
    private ChunkCoordinate lastChestChunk;
    private float nextUpdateTime;
    private bool isInitialized = false;
    private int chunksSinceLastBoss = 0;
    private int chunksSinceLastChest = 0;
    
    // Agent tracking data
    private class AgentSpawnData
    {
        public Vector3 spawnPosition;
        public Quaternion spawnRotation;
        public GameObject prefab;
        public bool isDead;
    }
    private Dictionary<ChunkCoordinate, List<AgentSpawnData>> agentSpawnDataPerChunk = new Dictionary<ChunkCoordinate, List<AgentSpawnData>>();
    
    // Cached for performance
    private List<ChunkCoordinate> chunksToRemove = new List<ChunkCoordinate>();
    
    // Race system caching (GC-free)
    private int currentChunkLevel = 1;
    
    [ShowInInspector, ReadOnly]
    [BoxGroup("Runtime Info")]
    private int totalVisitedChunks => visitedChunks.Count;
    
    [ShowInInspector, ReadOnly]
    [BoxGroup("Runtime Info")]
    private int totalSpawnedContent
    {
        get
        {
            int count = 0;
            foreach (var list in spawnedContentPerChunk.Values)
            {
                count += list.Count;
            }
            return count;
        }
    }
    
    [ShowInInspector, ReadOnly]
    [BoxGroup("Runtime Info")]
    private int totalBossesSpawned
    {
        get
        {
            int count = 0;
            foreach (var kvp in chunkContentTypes)
            {
                if (kvp.Value == ChunkContentType.Boss)
                    count++;
            }
            return count;
        }
    }
    
    [ShowInInspector, ReadOnly]
    [BoxGroup("Runtime Info")]
    private int totalChestsSpawned
    {
        get
        {
            int count = 0;
            foreach (var kvp in chunkContentTypes)
            {
                if (kvp.Value == ChunkContentType.TreasureChest)
                    count++;
            }
            return count;
        }
    }
    
    private void Start()
    {
        if (chunkManager == null || playerTransform == null || bossPrefab == null || chestPrefab == null || mainCamera == null || enemySpawnConfig == null)
        {
            // Attempt to fallback to Camera.main if not assigned
            if (mainCamera == null)
            {
                mainCamera = Camera.main;
            }

            if (chunkManager == null || playerTransform == null || bossPrefab == null || chestPrefab == null || mainCamera == null || enemySpawnConfig == null)
            {
                Debug.LogError("ChunkContentSpawner: Missing required references!");
                enabled = false;
                return;
            }
        }
        
        // Get starting chunk (this chunk won't spawn agents)
        startingChunk = chunkManager.GetCurrentPlayerChunk();
        currentPlayerChunk = startingChunk;
        visitedChunks.Add(startingChunk);
        
        // Try to find graph mover if not assigned
        if (graphMover == null)
        {
            graphMover = GameObject.FindObjectOfType<ChunkBasedGraphMover>();
            if (graphMover == null)
            {
                Debug.LogWarning("ChunkContentSpawner: ChunkBasedGraphMover not found! Agents may spawn before pathfinding is ready.");
            }
            else if (showDebugInfo)
            {
                Debug.Log("ChunkContentSpawner: Found ChunkBasedGraphMover automatically.");
            }
        }

        isInitialized = true;

        if (showDebugInfo)
        {
            Debug.Log($"ChunkContentSpawner: Initialized. Starting chunk: {startingChunk}");
        }
    }
    
    private void Update()
    {
        if (!isInitialized || Time.time < nextUpdateTime)
            return;
            
        nextUpdateTime = Time.time + updateInterval;
        
        // Check current player chunk
        ChunkCoordinate playerChunk = chunkManager.GetCurrentPlayerChunk();
        
        // Check if player moved to a new chunk
        if (playerChunk.x != currentPlayerChunk.x || playerChunk.y != currentPlayerChunk.y)
        {
            currentPlayerChunk = playerChunk;
            OnPlayerChunkChanged(playerChunk);
        }
        
        // Check for chunks to despawn
        CheckAndDespawnDistantContent();
    }
    
    private void OnPlayerChunkChanged(ChunkCoordinate newChunk)
    {
        // Don't spawn in the starting chunk
        if (newChunk.x == startingChunk.x && newChunk.y == startingChunk.y)
            return;
            
        // Check if this is a new chunk we haven't visited
        if (!visitedChunks.Contains(newChunk))
        {
            visitedChunks.Add(newChunk);
            chunksSinceLastBoss++;
            chunksSinceLastChest++;
            StartCoroutine(SpawnContentInChunkWhenReady(newChunk));
        }
        else
        {
            // Re-entering a previously visited chunk
            // Check if we have agent spawn data for this chunk
            if (agentSpawnDataPerChunk.TryGetValue(newChunk, out List<AgentSpawnData> spawnDataList))
            {
                StartCoroutine(RespawnAgentsInChunkWhenReady(newChunk, spawnDataList));
            }
        }
    }
    
    private ChunkContentType DetermineChunkContent()
    {
        float roll = UnityEngine.Random.value;
        
        // Check special spawns first with distance requirements
        if (roll < bossSpawnChance && chunksSinceLastBoss >= minChunksBetweenBosses)
        {
            chunksSinceLastBoss = 0;
            return ChunkContentType.Boss;
        }
        else if (roll < chestSpawnChance && chunksSinceLastChest >= minChunksBetweenChests)
        {
            chunksSinceLastChest = 0;
            return ChunkContentType.TreasureChest;
        }
        else if (roll < enemySpawnChance)
        {
            return ChunkContentType.Enemies;
        }
        
        return ChunkContentType.None;
    }
    
    private void SpawnContentInChunk(ChunkCoordinate chunk)
    {
        ChunkContentType contentType = DetermineChunkContent();
        chunkContentTypes[chunk] = contentType;
        
        if (contentType == ChunkContentType.None)
        {
            if (showDebugInfo)
            {
                Debug.Log($"ChunkContentSpawner: No content spawned in chunk {chunk}");
            }
            return;
        }
        
        Bounds chunkBounds = chunkManager.GetChunkBounds(chunk);
        List<GameObject> spawnedContent = new List<GameObject>();
        
        switch (contentType)
        {
            case ChunkContentType.Enemies:
                SpawnEnemies(chunk, chunkBounds, spawnedContent);
                break;
            case ChunkContentType.Boss:
                SpawnBoss(chunk, chunkBounds, spawnedContent);
                lastBossChunk = chunk;
                break;
            case ChunkContentType.TreasureChest:
                SpawnChest(chunk, chunkBounds, spawnedContent);
                lastChestChunk = chunk;
                break;
        }
        
        if (spawnedContent.Count > 0)
        {
            spawnedContentPerChunk[chunk] = spawnedContent;
        }
    }
    
    private void SpawnEnemies(ChunkCoordinate chunk, Bounds chunkBounds, List<GameObject> spawnedContent)
    {
        SpawnEnemiesWithRaceSystem(chunk, chunkBounds, spawnedContent);
    }
    
    private void SpawnBoss(ChunkCoordinate chunk, Bounds chunkBounds, List<GameObject> spawnedContent)
    {
        // Boss spawns in center of chunk
        Vector3 spawnPos = chunkBounds.center;
        
        // Only spawn if not visible
        if (!IsPositionVisibleToPlayer(spawnPos))
        {
            GameObject boss = PoolManager.Instance.Spawn(bossPrefab, spawnPos, Quaternion.identity);
            if (boss != null)
            {
                spawnedContent.Add(boss);
                
                // Set chunk bounds on the boss
                var bossMovement = boss.GetComponent<PathfindingMovement>();
                if (bossMovement != null)
                {
                    bossMovement.SetChunkBounds(chunkBounds);
                }
                
                // Track spawn data for boss
                if (!agentSpawnDataPerChunk.ContainsKey(chunk))
                {
                    agentSpawnDataPerChunk[chunk] = new List<AgentSpawnData>();
                }
                
                agentSpawnDataPerChunk[chunk].Add(new AgentSpawnData
                {
                    spawnPosition = spawnPos,
                    spawnRotation = Quaternion.identity,
                    prefab = bossPrefab,
                    isDead = false
                });
                
                if (showDebugInfo)
                {
                    Debug.Log($"ChunkContentSpawner: Spawned BOSS in chunk {chunk}");
                }
            }
        }
    }
    
    private void SpawnChest(ChunkCoordinate chunk, Bounds chunkBounds, List<GameObject> spawnedContent)
    {
        // Chest spawns at a random position away from edges
        float margin = chunkBounds.size.x * 0.3f;
        Vector3 spawnPos = new Vector3(
            Random.Range(chunkBounds.min.x + margin, chunkBounds.max.x - margin),
            Random.Range(chunkBounds.min.y + margin, chunkBounds.max.y - margin),
            0f
        );
        
        // Only spawn if not visible
        if (!IsPositionVisibleToPlayer(spawnPos))
        {
            GameObject chest = PoolManager.Instance.Spawn(chestPrefab, spawnPos, Quaternion.identity);
            if (chest != null)
            {
                spawnedContent.Add(chest);
                
                if (showDebugInfo)
                {
                    Debug.Log($"ChunkContentSpawner: Spawned CHEST in chunk {chunk}");
                }
            }
        }
    }
    
    private Vector3 FindValidSpawnPosition(Bounds chunkBounds, Vector3 chunkCenter, Vector3 dirFromPlayer)
    {
        const int MaxAttempts = 15;
        int attempts = 0;
        
        // Calculate offset center based on player approach direction
        // dirFromPlayer points from player to chunk center, we want to offset in that direction
        float offsetDistance = Mathf.Min(chunkBounds.size.x, chunkBounds.size.y) * 0.25f; // Offset by 25% of chunk size
        Vector3 offsetCenter = chunkCenter + (dirFromPlayer * offsetDistance);
        
        // Clamp offset center to stay within chunk bounds (with margin)
        float margin = spawnMargin * 2f;
        offsetCenter.x = Mathf.Clamp(offsetCenter.x, chunkBounds.min.x + margin, chunkBounds.max.x - margin);
        offsetCenter.y = Mathf.Clamp(offsetCenter.y, chunkBounds.min.y + margin, chunkBounds.max.y - margin);
        
        while (attempts < MaxAttempts)
        {
            attempts++;
            Vector3 spawnPos;
            
            // Spawn around the offset center (away from player entry)
            float spawnRadius = 0.2f; // Spawn within 20% of chunk size from offset center
            float randomAngle = Random.Range(0f, Mathf.PI * 2f);
            float randomDistance = Random.Range(0f, Mathf.Min(chunkBounds.size.x, chunkBounds.size.y) * spawnRadius);
            
            spawnPos = offsetCenter + new Vector3(
                Mathf.Cos(randomAngle) * randomDistance,
                Mathf.Sin(randomAngle) * randomDistance,
                0f
            );
            
            // Ensure position is within chunk bounds
            spawnPos.x = Mathf.Clamp(spawnPos.x, chunkBounds.min.x + spawnMargin, chunkBounds.max.x - spawnMargin);
            spawnPos.y = Mathf.Clamp(spawnPos.y, chunkBounds.min.y + spawnMargin, chunkBounds.max.y - spawnMargin);
            
            // Ensure the position is not currently visible by the player's camera
            if (!IsPositionVisibleToPlayer(spawnPos))
            {
                return spawnPos;
            }
        }
        
        // Fallback: Use offset center directly
        if (showDebugInfo)
        {
            Debug.LogWarning($"ChunkContentSpawner: Using fallback spawn position at offset center.");
        }
        
        return offsetCenter;
    }
    
    // Returns true if the given world position is currently inside the camera's viewport (with a small margin)
    private bool IsPositionVisibleToPlayer(Vector3 worldPos)
    {
        if (mainCamera == null) return false;
        Vector3 viewportPoint = mainCamera.WorldToViewportPoint(worldPos);
        // The point is visible if it lies within the [0,1] range on x and y and is in front of the camera (z>0)
        return viewportPoint.z > 0f && viewportPoint.x >= 0f && viewportPoint.x <= 1f && viewportPoint.y >= 0f && viewportPoint.y <= 1f;
    }
    
    private void CheckAndDespawnDistantContent()
    {
        chunksToRemove.Clear();
        
        // Check each chunk with spawned content
        foreach (var kvp in spawnedContentPerChunk)
        {
            ChunkCoordinate chunk = kvp.Key;
            
            // Despawn if not in current player chunk
            if (chunk.x != currentPlayerChunk.x || chunk.y != currentPlayerChunk.y)
            {
                chunksToRemove.Add(chunk);
            }
        }
        
        // Despawn content in distant chunks
        foreach (var chunk in chunksToRemove)
        {
            DespawnContentInChunk(chunk);
        }
    }
    
    private void DespawnContentInChunk(ChunkCoordinate chunk)
    {
        if (!spawnedContentPerChunk.TryGetValue(chunk, out List<GameObject> content))
            return;
            
        // Before despawning, check which agents are still alive
        if (agentSpawnDataPerChunk.TryGetValue(chunk, out List<AgentSpawnData> spawnDataList))
        {
            int agentIndex = 0;
            foreach (var obj in content)
            {
                if (obj != null)
                {
                    // Check if this is an enemy (has CombatantHealth component)
                    var enemyHealth = obj.GetComponent<CombatantHealth>();
                    if (enemyHealth != null && agentIndex < spawnDataList.Count)
                    {
                        // Mark as dead if health is 0 or dying
                        spawnDataList[agentIndex].isDead = enemyHealth.CurrentHealth <= 0;
                        agentIndex++;
                    }
                }
            }
        }
        
        int despawnCount = 0;
        foreach (var obj in content)
        {
            if (obj != null)
            {
                PoolManager.Instance.Despawn(obj);
                despawnCount++;
            }
        }
        
        spawnedContentPerChunk.Remove(chunk);
        
        if (showDebugInfo)
        {
            ChunkContentType contentType = chunkContentTypes.GetValueOrDefault(chunk, ChunkContentType.None);
            int aliveCount = 0;
            if (agentSpawnDataPerChunk.TryGetValue(chunk, out var spawnData))
            {
                aliveCount = spawnData.Count(data => !data.isDead);
            }
            Debug.Log($"ChunkContentSpawner: Despawned {despawnCount} {contentType} from chunk {chunk}. Alive agents: {aliveCount}");
        }
    }
    
    private IEnumerator SpawnContentInChunkWhenReady(ChunkCoordinate chunk)
    {
        // Wait for graph mover to be ready if it exists
        if (graphMover != null)
        {
            // Wait a frame to let graph mover update first
            yield return null;
            
            // Wait while graph is scanning
            if (graphMover.IsScanning())
            {
                if (showDebugInfo)
                {
                    Debug.Log($"ChunkContentSpawner: Waiting for pathfinding graph to be ready for chunk {chunk}...");
                }
                
                while (graphMover.IsScanning())
                {
                    yield return new WaitForSeconds(0.1f);
                }
                
                // Small additional delay to ensure graph is fully ready
                yield return new WaitForSeconds(0.1f);
                
                if (showDebugInfo)
                {
                    Debug.Log($"ChunkContentSpawner: Pathfinding graph ready, spawning content in chunk {chunk}");
                }
            }
        }
        
        SpawnContentInChunk(chunk);
    }
    
    private IEnumerator RespawnAgentsInChunkWhenReady(ChunkCoordinate chunk, List<AgentSpawnData> spawnDataList)
    {
        // Wait for graph mover to be ready if it exists
        if (graphMover != null)
        {
            // Wait a frame to let graph mover update first
            yield return null;
            
            // Wait while graph is scanning
            if (graphMover.IsScanning())
            {
                if (showDebugInfo)
                {
                    Debug.Log($"ChunkContentSpawner: Waiting for pathfinding graph to be ready for respawn in chunk {chunk}...");
                }
                
                while (graphMover.IsScanning())
                {
                    yield return new WaitForSeconds(0.1f);
                }
                
                // Small additional delay to ensure graph is fully ready
                yield return new WaitForSeconds(0.1f);
                
                if (showDebugInfo)
                {
                    Debug.Log($"ChunkContentSpawner: Pathfinding graph ready, respawning agents in chunk {chunk}");
                }
            }
        }
        
        RespawnAgentsInChunk(chunk, spawnDataList);
    }
    
    private void RespawnAgentsInChunk(ChunkCoordinate chunk, List<AgentSpawnData> spawnDataList)
    {
        Bounds chunkBounds = chunkManager.GetChunkBounds(chunk);
        List<GameObject> spawnedContent = new List<GameObject>();
        
        int respawnCount = 0;
        int skippedDead = 0;
        
        foreach (var spawnData in spawnDataList)
        {
            // Skip dead agents
            if (spawnData.isDead)
            {
                skippedDead++;
                continue;
            }
            
            // Only respawn if position is not visible
            if (!IsPositionVisibleToPlayer(spawnData.spawnPosition))
            {
                GameObject agent = PoolManager.Instance.Spawn(spawnData.prefab, spawnData.spawnPosition, spawnData.spawnRotation);
                if (agent != null)
                {
                    spawnedContent.Add(agent);
                    respawnCount++;
                    
                    // Set chunk bounds on the enemy
                    var pathfindingMovement = agent.GetComponent<PathfindingMovement>();
                    if (pathfindingMovement != null)
                    {
                        pathfindingMovement.SetChunkBounds(chunkBounds);
                    }
                }
            }
        }
        
        // Store respawned content
        if (spawnedContent.Count > 0)
        {
            spawnedContentPerChunk[chunk] = spawnedContent;
        }
        
        if (showDebugInfo)
        {
            Debug.Log($"ChunkContentSpawner: Respawned {respawnCount} agents in chunk {chunk} (skipped {skippedDead} dead agents)");
        }
    }
    
    #region Race System Methods
    
    /// <summary>
    /// Spawns enemies using the race system with weighted selection
    /// </summary>
    private void SpawnEnemiesWithRaceSystem(ChunkCoordinate chunk, Bounds chunkBounds, List<GameObject> spawnedContent)
    {
        // Calculate chunk level (distance from starting chunk)
        int chunkLevel = CalculateChunkLevel(chunk);
        
        // Try to cast the ScriptableObject to EnemySpawnConfiguration
        var spawnConfig = enemySpawnConfig as EnemySpawnConfiguration;
        if (spawnConfig == null)
        {
            if (showDebugInfo)
            {
                Debug.LogError("ChunkContentSpawner: enemySpawnConfig is not of type EnemySpawnConfiguration!");
            }
            return;
        }
        
        // Check if enemies should spawn at all
        if (!spawnConfig.ShouldSpawnEnemies())
        {
            if (showDebugInfo)
            {
                Debug.Log($"ChunkContentSpawner: Skipped enemy spawn in chunk {chunk} due to spawn chance");
            }
            return;
        }
        
        // Get available races for this chunk level
        var availableRaces = spawnConfig.GetAvailableRaces(chunkLevel);
        if (availableRaces == null || availableRaces.Length == 0)
        {
            if (showDebugInfo)
            {
                Debug.LogWarning($"ChunkContentSpawner: No races available for chunk level {chunkLevel} in chunk {chunk}");
            }
            return;
        }
        
        // Determine enemy count with scaling
        int enemyCount = spawnConfig.GetScaledEnemyCount(chunkLevel, CalculateDistanceFromSpawn(chunk));
        
        // Create spawn data list for this chunk
        List<AgentSpawnData> spawnDataList = new List<AgentSpawnData>();
        
        // Spawn enemies based on race selection
        SpawnEnemiesFromRaces(chunk, chunkBounds, spawnedContent, spawnDataList, availableRaces, enemyCount, spawnConfig);
        
        // Store spawn data for this chunk
        if (spawnDataList.Count > 0)
        {
            agentSpawnDataPerChunk[chunk] = spawnDataList;
        }
        
        if (showDebugInfo)
        {
            Debug.Log($"ChunkContentSpawner: Spawned {spawnedContent.Count} enemies from {availableRaces.Length} available races in chunk {chunk} (level {chunkLevel})");
        }
    }
    
    /// <summary>
    /// Spawns enemies from selected races
    /// </summary>
    private void SpawnEnemiesFromRaces(ChunkCoordinate chunk, Bounds chunkBounds, List<GameObject> spawnedContent, 
        List<AgentSpawnData> spawnDataList, WeightedEnemyRace[] availableRaces, int enemyCount, EnemySpawnConfiguration spawnConfig)
    {
        Vector3 playerPos = playerTransform.position;
        Vector3 chunkCenter = chunkBounds.center;
        Vector3 dirFromPlayer = (chunkCenter - playerPos).normalized;
        
        int spawnedCount = 0;
        bool shouldSpawnMultipleRaces = spawnConfig.ShouldSpawnMultipleRaces() && availableRaces.Length > 1;
        
        while (spawnedCount < enemyCount)
        {
            // Select race using simple weighted selection
            WeightedEnemyRace selectedRace = SelectRaceWeighted(availableRaces);
            if (selectedRace?.RaceData == null)
                break;
                
            // Get available enemy types for current chunk level
            int chunkLevel = CalculateChunkLevel(chunk);
            var availableTypes = selectedRace.RaceData.GetAvailableEnemyTypes(chunkLevel);
            if (availableTypes == null || availableTypes.Length == 0)
                continue;
                
            // Determine how many enemies to spawn from this race
            int racialSpawnCount = shouldSpawnMultipleRaces ? 
                Random.Range(selectedRace.RaceData.MinEnemiesPerSpawn, selectedRace.RaceData.MaxEnemiesPerSpawn + 1) :
                enemyCount - spawnedCount;
                
            racialSpawnCount = Mathf.Min(racialSpawnCount, enemyCount - spawnedCount);
            
            // Spawn enemies from this race
            for (int i = 0; i < racialSpawnCount && spawnedCount < enemyCount; i++)
            {
                WeightedEnemyType selectedType = SelectEnemyTypeWeighted(availableTypes);
                if (selectedType?.Prefab == null)
                    continue;
                    
                Vector3 spawnPos = FindValidSpawnPosition(chunkBounds, chunkCenter, dirFromPlayer);
                if (spawnPos != Vector3.zero)
                {
                    GameObject enemy = PoolManager.Instance.Spawn(selectedType.Prefab, spawnPos, Quaternion.identity);
                    if (enemy != null)
                    {
                        spawnedContent.Add(enemy);
                        spawnedCount++;
                        
                        // Set chunk bounds on the enemy
                        var pathfindingMovement = enemy.GetComponent<PathfindingMovement>();
                        if (pathfindingMovement != null)
                        {
                            pathfindingMovement.SetChunkBounds(chunkBounds);
                        }
                        
                        // Track spawn data
                        spawnDataList.Add(new AgentSpawnData
                        {
                            spawnPosition = spawnPos,
                            spawnRotation = Quaternion.identity,
                            prefab = selectedType.Prefab,
                            isDead = false
                        });
                    }
                }
            }
            
            // If not spawning multiple races, exit after first race
            if (!shouldSpawnMultipleRaces)
                break;
        }
    }
    
    /// <summary>
    /// Simple weighted race selection without GC allocation
    /// </summary>
    private WeightedEnemyRace SelectRaceWeighted(WeightedEnemyRace[] races)
    {
        if (races == null || races.Length == 0)
            return null;
            
        float totalWeight = 0f;
        foreach (var race in races)
        {
            if (race != null && race.Weight > 0f)
                totalWeight += race.Weight;
        }
        
        if (totalWeight <= 0f)
            return races[0]; // Fallback to first race
            
        float randomValue = Random.value * totalWeight;
        float currentWeight = 0f;
        
        foreach (var race in races)
        {
            if (race != null && race.Weight > 0f)
            {
                currentWeight += race.Weight;
                if (randomValue <= currentWeight)
                    return race;
            }
        }
        
        return races[races.Length - 1]; // Fallback to last race
    }
    
    /// <summary>
    /// Simple weighted enemy type selection without GC allocation
    /// </summary>
    private WeightedEnemyType SelectEnemyTypeWeighted(WeightedEnemyType[] types)
    {
        if (types == null || types.Length == 0)
            return null;
            
        float totalWeight = 0f;
        foreach (var type in types)
        {
            if (type != null && type.Weight > 0f)
                totalWeight += type.Weight;
        }
        
        if (totalWeight <= 0f)
            return types[0]; // Fallback to first type
            
        float randomValue = Random.value * totalWeight;
        float currentWeight = 0f;
        
        foreach (var type in types)
        {
            if (type != null && type.Weight > 0f)
            {
                currentWeight += type.Weight;
                if (randomValue <= currentWeight)
                    return type;
            }
        }
        
        return types[types.Length - 1]; // Fallback to last type
    }
    
    /// <summary>
    /// Calculates the level of a chunk based on distance from starting chunk
    /// </summary>
    private int CalculateChunkLevel(ChunkCoordinate chunk)
    {
        int deltaX = Mathf.Abs(chunk.x - startingChunk.x);
        int deltaY = Mathf.Abs(chunk.y - startingChunk.y);
        return Mathf.Max(deltaX, deltaY) + 1; // Manhattan distance + 1 (so starting chunk is level 1)
    }
    
    /// <summary>
    /// Calculates the Manhattan distance from starting chunk
    /// </summary>
    private int CalculateDistanceFromSpawn(ChunkCoordinate chunk)
    {
        int deltaX = Mathf.Abs(chunk.x - startingChunk.x);
        int deltaY = Mathf.Abs(chunk.y - startingChunk.y);
        return deltaX + deltaY;
    }
    
    #endregion
    
    private void OnDestroy()
    {
        // Clean up all spawned content
        foreach (var kvp in spawnedContentPerChunk)
        {
            foreach (var obj in kvp.Value)
            {
                if (obj != null && PoolManager.Instance != null)
                {
                    PoolManager.Instance.Despawn(obj);
                }
            }
        }
        
        spawnedContentPerChunk.Clear();
        chunkContentTypes.Clear();
        agentSpawnDataPerChunk.Clear();
    }
    
    private void OnDrawGizmosSelected()
    {
        if (!isInitialized || !showDebugInfo)
            return;
            
        // Draw current player chunk
        Gizmos.color = Color.green;
        DrawChunkGizmo(currentPlayerChunk);
        
        // Draw chunks with content
        foreach (var kvp in chunkContentTypes)
        {
            switch (kvp.Value)
            {
                case ChunkContentType.Enemies:
                    Gizmos.color = Color.yellow;
                    break;
                case ChunkContentType.Boss:
                    Gizmos.color = Color.red;
                    break;
                case ChunkContentType.TreasureChest:
                    Gizmos.color = Color.cyan;
                    break;
                default:
                    continue;
            }
            DrawChunkGizmo(kvp.Key);
        }
        
        // Draw spawn positions
        foreach (var kvp in spawnedContentPerChunk)
        {
            ChunkContentType contentType = chunkContentTypes.GetValueOrDefault(kvp.Key, ChunkContentType.None);
            
            switch (contentType)
            {
                case ChunkContentType.Enemies:
                    Gizmos.color = Color.yellow;
                    break;
                case ChunkContentType.Boss:
                    Gizmos.color = Color.red;
                    break;
                case ChunkContentType.TreasureChest:
                    Gizmos.color = Color.cyan;
                    break;
            }
            
            foreach (var obj in kvp.Value)
            {
                if (obj != null)
                {
                    float size = contentType == ChunkContentType.Boss ? 1f : 0.5f;
                    Gizmos.DrawWireSphere(obj.transform.position, size);
                }
            }
        }
    }
    
    private void DrawChunkGizmo(ChunkCoordinate chunk)
    {
        if (chunkManager == null) return;
        
        Bounds bounds = chunkManager.GetChunkBounds(chunk);
        Gizmos.DrawWireCube(bounds.center, bounds.size);
    }
}