using UnityEngine;
using System.Collections;
using Sirenix.OdinInspector;

/// <summary>
/// Ranged attack strategy for enemies. Spawns projectiles or instant spells aimed at the target.
/// Enhanced with critical hits and multi-projectile/spell support.
/// </summary>
[System.Serializable]
public class RangedAttackStrategy : IAttackStrategy
{
        public enum RangedAttackType
        {
            Projectile,
            InstantSpell
        }
        
        [SerializeField] private RangedAttackType attackType = RangedAttackType.Projectile;
        [SerializeField] private float damage = 15f;
        [SerializeField] private float attackRange = 8.0f;
        [SerializeField] private float attackCooldown = 2.0f;
        
        [SerializeField, ShowIf("attackType", RangedAttackType.Projectile)] 
        private float projectileSpeed = 10f;
        [SerializeField, ShowIf("attackType", RangedAttackType.Projectile)] 
        private GameObject projectilePrefab;
        
        [SerializeField, ShowIf("attackType", RangedAttackType.InstantSpell)] 
        private GameObject instantSpellPrefab;
        [Serial<PERSON><PERSON><PERSON>, ShowIf("attackType", RangedAttackType.InstantSpell), <PERSON><PERSON><PERSON>("Radius for multi-spell spread pattern")] 
        private float multiSpellSpreadRadius = 2f;
        
        [SerializeField] private Transform spawnPoint;
        [SerializeField] private string animationName = "attack";
        [SerializeField] private bool requiresAnimation = true;
#if UNITY_EDITOR
        [SerializeField, Tooltip("Enable debug logs for RangedAttackStrategy (Editor only).")]
        private bool enableDebugLogs = false;
#endif
        
        // Modifier system
        private AttackModifierCollection modifiers = new AttackModifierCollection();
        
        // Cooldown management
        private float lastAttackTime = -999f;
        
        // Animation event handling
        private Transform currentAttacker;
        private Transform currentTarget;
        private System.Action attackCompletionCallback;
        private bool projectileFired = false;
        
        // Cached components (set via SetupComponents)
        private SpriteAnimator cachedSpriteAnimator;
        private EnemyAnimationController cachedAnimationController;
        
        // IAttackStrategy Implementation
        public float Damage 
        { 
            get => damage; 
            set => damage = value; 
        }
        
        public float AttackRange => modifiers.GetModifiedRange(attackRange);
        public float AttackCooldown => modifiers.GetModifiedCooldown(attackCooldown);
        public string AttackAnimationName => animationName;
        public bool RequiresAnimation => requiresAnimation;
        public AttackModifierCollection Modifiers => modifiers;
        
        // Cooldown properties (using modified cooldown)
        public bool IsOnCooldown => Time.time - lastAttackTime < AttackCooldown;
        public float CooldownRemaining => Mathf.Max(0f, AttackCooldown - (Time.time - lastAttackTime));
        
        // Additional Properties
        public float ProjectileSpeed 
        { 
            get => projectileSpeed; 
            set => projectileSpeed = value; 
        }
        
        public GameObject ProjectilePrefab 
        { 
            get => projectilePrefab; 
            set => projectilePrefab = value; 
        }
        
        public Transform SpawnPoint 
        { 
            get => spawnPoint; 
            set => spawnPoint = value; 
        }
        
        public bool CanAttack(Transform target, float distanceToTarget)
        {
            if (target == null) return false;
            if (distanceToTarget > attackRange) return false;
            if (IsOnCooldown) return false;
            
            // Check appropriate prefab based on attack type
            if (attackType == RangedAttackType.Projectile && projectilePrefab == null) return false;
            if (attackType == RangedAttackType.InstantSpell && instantSpellPrefab == null) return false;
            
            // Check if target is the player using collision layer system
            if (target == PlayerManager.PlayerGameObject)
            {
                return true;
            }
            
            // Fallback: check SpatialCollider layer
            if (target.TryGetComponent<SpatialCollider>(out var spatialCollider))
            {
                return spatialCollider.Layer.HasFlag(CollisionLayers.Player);
            }
            
            return false;
        }
        
        public void ExecuteAttackWithAnimation(Transform attacker, Transform target, System.Action onComplete = null)
        {
            if (!CanAttack(target, Vector3.Distance(attacker.position, target.position)))
            {
#if UNITY_EDITOR
                if (enableDebugLogs)
                    Debug.Log($"[RangedAttack] {attacker.name}: Cannot attack - CanAttack returned false");
#endif
                onComplete?.Invoke();
                return;
            }
            
#if UNITY_EDITOR
            if (enableDebugLogs)
                Debug.Log($"[RangedAttack] {attacker.name}: Starting attack execution, RequiresAnimation: {RequiresAnimation}, AnimationName: '{animationName}'");
#endif
            
            // Start cooldown immediately to prevent rapid attacks if interrupted
            StartCooldown();
            
            if (RequiresAnimation)
            {
                // Handle animation-based attack with frame events
                if (cachedSpriteAnimator != null && cachedSpriteAnimator.HasAnimation(animationName))
                {
#if UNITY_EDITOR
                    if (enableDebugLogs)
                        Debug.Log($"[RangedAttack] {attacker.name}: Found SpriteAnimator and animation '{animationName}', setting up event listeners");
#endif
                    
                    // Store target and completion callback for frame event handling
                    currentAttacker = attacker;
                    currentTarget = target;
                    attackCompletionCallback = onComplete;
                    projectileFired = false;
                    
                    // First, clean up any existing event subscriptions to avoid double-triggers
                    cachedSpriteAnimator.OnAnimationEvent -= OnAnimationFrameEvent;
                    cachedSpriteAnimator.OnAnimationCompleted -= OnAnimationCompleted;
                    
                    // Apply attack speed modifier to animation
                    float attackSpeedBonus = modifiers.GetAttackSpeedBonus();
                    float animationSpeedMultiplier = 1f + attackSpeedBonus;
                    cachedSpriteAnimator.SetTemporarySpeedMultiplier(animationSpeedMultiplier);
                    
                    // EnemyAnimationController is interfering - disable it during attack
                    if (cachedAnimationController != null)
                    {
                        cachedAnimationController.enabled = false;
#if UNITY_EDITOR
                        if (enableDebugLogs)
                            Debug.Log($"[RangedAttack] {attacker.name}: Disabled EnemyAnimationController to prevent interference");
#endif
                    }
                    
                    // Subscribe to animation events BEFORE starting the animation
                    // This ensures we catch all events from the new animation
                    cachedSpriteAnimator.OnAnimationEvent += OnAnimationFrameEvent;
                    cachedSpriteAnimator.OnAnimationCompleted += OnAnimationCompleted;
                    
                    // Use PlayForced to ensure animation starts from beginning for precise frame events
                    cachedSpriteAnimator.PlayForced(animationName);
                    
                    // Since SpriteAnimator OnAnimationCompleted is buggy, use a timer-based fallback
                    if (cachedSpriteAnimator != null)
                    {
                        cachedSpriteAnimator.StartCoroutine(CheckAnimationAfterDelay(attacker.name));
                        // Start a timer for expected animation duration (most attack animations are ~1 second)
                        cachedSpriteAnimator.StartCoroutine(AnimationCompletionTimer(1.0f));
                    }
                    
#if UNITY_EDITOR
                    if (enableDebugLogs)
                    {
                        Debug.Log($"[RangedAttack] {attacker.name}: Started attack animation '{animationName}', subscribed to events, waiting for frame events...");
                        Debug.Log($"[RangedAttack] {attacker.name}: Animation state - IsPlaying: {cachedSpriteAnimator.IsPlaying}, CurrentAnimation: '{cachedSpriteAnimator.CurrentAnimation}'");
                        Debug.Log($"[RangedAttack] {attacker.name}: EnemyAnimationController enabled: {cachedAnimationController?.enabled ?? false}");
                    }
#endif
                }
                else
                {
#if UNITY_EDITOR
                    if (enableDebugLogs)
                        Debug.Log($"[RangedAttack] {attacker.name}: No SpriteAnimator or animation '{animationName}' found, executing immediate attack");
#endif
                    
                    // No animation available, execute immediately
                    SpawnAttack(attacker, target);
                    onComplete?.Invoke();
                }
            }
            else
            {
#if UNITY_EDITOR
                if (enableDebugLogs)
                    Debug.Log($"[RangedAttack] {attacker.name}: No animation required, executing immediate attack");
#endif
                
                // No animation required, execute immediately
                SpawnAttack(attacker, target);
                onComplete?.Invoke();
            }
        }
        
        public void ExecuteAttack(Transform attacker, Transform target)
        {
            // Legacy method - calls new method without completion callback
            ExecuteAttackWithAnimation(attacker, target, null);
        }
        
        private void SpawnAttack(Transform attacker, Transform target)
        {
            // Calculate damage with modifiers (including crit)
            CriticalHitResult damageResult = modifiers.CalculateDamage(damage);
            string dbgInfo = $"Base:{damage}, Final:{damageResult.finalDamage}, Crit:{damageResult.isCritical}, Mods:{modifiers.GetAllModifiers().Count}";
            
            // Get number of attacks to spawn
            int attackCount = modifiers.GetProjectileCount(); // This applies to both projectiles and spells
            
#if UNITY_EDITOR
            if (enableDebugLogs && (attackCount > 1 || damageResult.isCritical))
            {
                string attackTypeStr = attackType == RangedAttackType.Projectile ? "projectile" : "spell";
                string logMsg = $"{attacker.name}: Firing {attackCount} {attackTypeStr}(s)";
                if (damageResult.isCritical)
                    logMsg += $" [CRITICAL HIT! {damageResult.finalDamage} damage]";
                else
                    logMsg += $" [{damageResult.finalDamage} damage]";
                Debug.Log(logMsg);
            }
#endif
            
            var poolManager = PoolManager.Instance;
            if (poolManager == null)
            {
#if UNITY_EDITOR
                if (enableDebugLogs)
                    Debug.LogWarning($"{attacker.name}: PoolManager not found for ranged attack!");
#endif
                return;
            }
            
            if (attackType == RangedAttackType.Projectile)
            {
                SpawnProjectiles(attacker, target, damageResult, attackCount, dbgInfo);
            }
            else // InstantSpell
            {
                SpawnInstantSpells(attacker, target, damageResult, attackCount, dbgInfo);
            }
        }
        
        private void SpawnProjectiles(Transform attacker, Transform target, CriticalHitResult damageResult, int projectileCount, string dbgInfo)
        {
            // Get spawn position (use spawnPoint if available, otherwise use attacker position)
            Vector3 spawnPosition = spawnPoint != null ? spawnPoint.position : attacker.position;
            
            // Calculate base direction to target
            Vector3 baseDirection = (target.position - spawnPosition).normalized;
            
            // Calculate modified projectile speed
            float modifiedSpeed = modifiers.GetModifiedProjectileSpeed(projectileSpeed);
            
            var poolManager = PoolManager.Instance;
            
            // Spawn multiple projectiles
            for (int i = 0; i < projectileCount; i++)
            {
                Vector3 direction = baseDirection;
                
                // Add spread for multiple projectiles
                if (projectileCount > 1)
                {
                    float spreadAngle = 8f; // degrees per projectile (reduced for tighter formation)
                    float totalSpread = (projectileCount - 1) * spreadAngle;
                    float currentAngle = -totalSpread * 0.5f + (i * spreadAngle);
                    
                    // Rotate direction by spread angle (use Z-axis for 2D rotation)
                    Quaternion rotation = Quaternion.AngleAxis(currentAngle, Vector3.forward);
                    direction = rotation * baseDirection;
                }
                
                GameObject projectileObj = poolManager.Spawn(projectilePrefab, spawnPosition, Quaternion.identity);
                
                if (projectileObj != null)
                {
                    if (projectileObj.TryGetComponent<Projectile>(out var projectile))
                    {
                        // Initialize with enemy projectile layer using actual damage value
                        projectile.Initialize(
                            spawnPosition,
                            direction,
                            (float)damageResult.finalDamage, // Pass actual damage, not multiplier
                            CollisionLayers.EnemyProjectile,
                            modifiedSpeed,
                            5f // lifetime
                        );
                        
                        // Apply special effects if available
                        ApplyProjectileEffects(projectile, damageResult);
                    }
                }
                
                PlayerManager.SetPendingDamageDebug(dbgInfo);
            }
        }
        
        private void SpawnInstantSpells(Transform attacker, Transform target, CriticalHitResult damageResult, int spellCount, string dbgInfo)
        {
            var poolManager = PoolManager.Instance;
            Vector3 targetPosition = target.position;
            
            // Spawn multiple spells
            for (int i = 0; i < spellCount; i++)
            {
                Vector3 spellPosition = targetPosition;
                
                // Add offset for multiple spells in a pattern
                if (spellCount > 1)
                {
                    // Create a circular pattern around the target
                    float angleStep = 360f / spellCount;
                    float angle = i * angleStep;
                    Vector3 offset = new Vector3(
                        Mathf.Cos(angle * Mathf.Deg2Rad) * multiSpellSpreadRadius,
                        Mathf.Sin(angle * Mathf.Deg2Rad) * multiSpellSpreadRadius,
                        0f
                    );
                    spellPosition += offset;
                }
                
                GameObject spellObj = poolManager.Spawn(instantSpellPrefab, spellPosition, Quaternion.identity);
                
                if (spellObj != null)
                {
                    if (spellObj.TryGetComponent<InstantSpell>(out var spell))
                    {
                        // Calculate damage multiplier using actual calculated damage
                        // Assuming base damage is 20 for instant spells (from InstantSpell default)
                        float damageMultiplier = (float)damageResult.finalDamage / 20f;
                        
                        // Initialize with enemy projectile layer
                        spell.Initialize(
                            spellPosition, 
                            damageMultiplier, 
                            CollisionLayers.EnemyProjectile
                        );
                    }
                }
                
                PlayerManager.SetPendingDamageDebug(dbgInfo);
            }
        }
        
        /// <summary>
        /// Apply special effects to projectiles based on modifiers
        /// </summary>
        private void ApplyProjectileEffects(Projectile projectile, CriticalHitResult damageResult)
        {
            // Visual effect for critical projectiles
            if (damageResult.isCritical)
            {
                // You could add particle effects, different colors, etc.
                // For now, just log it
#if UNITY_EDITOR
                if (enableDebugLogs)
                    Debug.Log($"Critical projectile spawned with {damageResult.critMultiplier:F1}x damage!");
#endif
            }
            
            // Apply pierce chance if available
            float pierceChance = modifiers.GetPierceChance();
            if (pierceChance > 0f && Random.value < pierceChance)
            {
                // Enable piercing on projectile if it supports it
                // This would require extending the Projectile class
#if UNITY_EDITOR
                if (enableDebugLogs)
                    Debug.Log($"Projectile gains piercing ability!");
#endif
            }
        }
        
        public void OnSpawnFromPool()
        {
            // Reset cooldown on spawn
            lastAttackTime = -999f;
            
            // Clear any runtime modifiers to avoid stacking across spawns
            modifiers.ClearModifiers();
        }
        
        public void OnReturnToPool()
        {
            // No special cleanup needed for ranged attacks
        }
        
        /// <summary>
        /// Constructor for creating ranged attack strategies programmatically.
        /// </summary>
        public RangedAttackStrategy() { }
        
        /// <summary>
        /// Constructor with custom parameters for projectile attacks.
        /// </summary>
        public RangedAttackStrategy(float damage, GameObject projectilePrefab, float attackRange = 8.0f, 
            float attackCooldown = 2.0f, float projectileSpeed = 10f, string animationName = "attack")
        {
            this.attackType = RangedAttackType.Projectile;
            this.damage = damage;
            this.projectilePrefab = projectilePrefab;
            this.attackRange = attackRange;
            this.attackCooldown = attackCooldown;
            this.projectileSpeed = projectileSpeed;
            this.animationName = animationName;
            this.lastAttackTime = -999f;
        }
        
        /// <summary>
        /// Constructor with custom parameters for instant spell attacks.
        /// </summary>
        public RangedAttackStrategy(float damage, GameObject instantSpellPrefab, RangedAttackType attackType,
            float attackRange = 8.0f, float attackCooldown = 2.0f, string animationName = "attack")
        {
            this.attackType = attackType;
            this.damage = damage;
            this.instantSpellPrefab = instantSpellPrefab;
            this.attackRange = attackRange;
            this.attackCooldown = attackCooldown;
            this.animationName = animationName;
            this.lastAttackTime = -999f;
        }
        
        public void StartCooldown()
        {
            lastAttackTime = Time.time;
        }
        
        /// <summary>
        /// Update attack modifiers from external sources (chunk buffs, etc.)
        /// </summary>
        public void UpdateModifiers(System.Collections.Generic.List<AttackModifier> externalModifiers)
        {
            // Clear existing external modifiers
            modifiers.ClearModifiers("chunk");
            modifiers.ClearModifiers("distance_scaling");
            modifiers.ClearModifiers("random");
            modifiers.ClearModifiers("elite_zone");
            
            // Add new external modifiers in batch (more efficient)
            modifiers.AddModifiers(externalModifiers);
        }
        
        /// <summary>
        /// Setup the attack strategy with required components.
        /// Called once during initialization to cache all needed components.
        /// </summary>
        public void SetupComponents(SpriteAnimator spriteAnimator, EnemyAnimationController animationController)
        {
            cachedSpriteAnimator = spriteAnimator;
            cachedAnimationController = animationController;
        }
        
        /// <summary>
        /// Handles animation frame events for precise projectile timing.
        /// Listens for 'shoot', 'fire', or 'projectile' events.
        /// </summary>
        private void OnAnimationFrameEvent(string eventName, string parameter)
        {
#if UNITY_EDITOR
            // Log ALL frame events to see what's happening
            if (enableDebugLogs)
                Debug.Log($"[RangedAttack] {currentAttacker?.name}: Animation frame event '{eventName}' received, projectileFired: {projectileFired}, currentAttacker: {currentAttacker != null}, currentTarget: {currentTarget != null}");
#endif
            
            // Check for shoot events (case-insensitive)
            string lowerEventName = eventName.ToLowerInvariant();
            if ((lowerEventName == "shoot" || lowerEventName == "fire" || lowerEventName == "projectile") 
                && !projectileFired && currentAttacker != null && currentTarget != null)
            {
#if UNITY_EDITOR
                if (enableDebugLogs)
                    Debug.Log($"[RangedAttack] {currentAttacker.name}: Firing projectile from animation event '{eventName}'");
#endif
                // Fire attack at exact frame
                SpawnAttack(currentAttacker, currentTarget);
                projectileFired = true;
                
            }
#if UNITY_EDITOR
            else if (enableDebugLogs)
            {
                string reason = !projectileFired ? "already fired" : 
                               currentAttacker == null ? "no attacker" : 
                               currentTarget == null ? "no target" : 
                               "event not recognized";
                Debug.Log($"[RangedAttack] {currentAttacker?.name}: Animation event '{eventName}' ignored - {reason}");
            }
#endif
        }
        
        /// <summary>
        /// Cleanup attack state variables.
        /// </summary>
        private void CleanupAttackState()
        {
            // ALWAYS unsubscribe from events to prevent them from triggering after attack
            if (cachedSpriteAnimator != null)
            {
                cachedSpriteAnimator.OnAnimationEvent -= OnAnimationFrameEvent;
                cachedSpriteAnimator.OnAnimationCompleted -= OnAnimationCompleted;
            }
            
            currentAttacker = null;
            currentTarget = null;
            attackCompletionCallback = null;
            projectileFired = false;
        }
        
        /// <summary>
        /// Handles animation completion for cleanup.
        /// </summary>
        private void OnAnimationCompleted(string animationName)
        {
#if UNITY_EDITOR
            // Log ALL animation completions to see what's happening
            if (enableDebugLogs)
                Debug.Log($"[RangedAttack] OnAnimationCompleted: '{animationName}', expected: '{this.animationName}', currentAttacker: {currentAttacker != null}, callback: {attackCompletionCallback != null}");
#endif
            
            // Only process if this is our attack animation AND we have an active attack in progress
            if (animationName == this.animationName && currentAttacker != null && attackCompletionCallback != null)
            {
#if UNITY_EDITOR
                if (enableDebugLogs)
                    Debug.Log($"[RangedAttack] {currentAttacker.name}: Animation '{animationName}' completed - processing cleanup");
#endif
                
                // Immediately unsubscribe to prevent multiple calls from looping animations
                if (cachedSpriteAnimator != null)
                {
                    cachedSpriteAnimator.OnAnimationEvent -= OnAnimationFrameEvent;
                    cachedSpriteAnimator.OnAnimationCompleted -= OnAnimationCompleted;
                    
                    // Clear temporary speed override
                    cachedSpriteAnimator.ClearTemporarySpeedMultiplier();
                }
                
                // Re-enable EnemyAnimationController
                if (currentAttacker != null)
                {
                    if (cachedAnimationController != null)
                    {
                        cachedAnimationController.enabled = true;
#if UNITY_EDITOR
                        if (enableDebugLogs)
                            Debug.Log($"[RangedAttack] {currentAttacker.name}: Re-enabled EnemyAnimationController");
#endif
                    }
                }
                
                
                // If no frame event fired the attack, fire it now (fallback)
                if (!projectileFired && currentAttacker != null && currentTarget != null)
                {
#if UNITY_EDITOR
                    if (enableDebugLogs)
                        Debug.Log($"[RangedAttack] {currentAttacker.name}: Animation completed without frame event - using fallback attack");
#endif
                    SpawnAttack(currentAttacker, currentTarget);
                }
#if UNITY_EDITOR
                else if (enableDebugLogs)
                {
                    string reason = projectileFired ? "already fired via frame event" : 
                                   currentAttacker == null ? "no attacker" : 
                                   "no target";
                    Debug.Log($"[RangedAttack] {currentAttacker?.name}: Animation completed - fallback skipped: {reason}");
                }
#endif
                
                // Notify completion and cleanup
                attackCompletionCallback?.Invoke();
                CleanupAttackState();
            }
#if UNITY_EDITOR
            else if (enableDebugLogs && animationName == this.animationName)
            {
                // Only log if it's our attack animation but other conditions aren't met
                string reason = currentAttacker == null ? "no current attacker" :
                               attackCompletionCallback == null ? "no completion callback" :
                               "unknown";
                Debug.Log($"[RangedAttack] Ignoring animation completion - {reason}");
            }
#endif
        }
        
        /// <summary>
        /// Debug coroutine to check if animation changes after a delay
        /// </summary>
        private System.Collections.IEnumerator CheckAnimationAfterDelay(string attackerName)
        {
            yield return new WaitForSeconds(0.1f); // Wait a few frames
            
#if UNITY_EDITOR
            if (enableDebugLogs && cachedSpriteAnimator != null)
            {
                Debug.Log($"[RangedAttack] {attackerName}: After delay - IsPlaying: {cachedSpriteAnimator.IsPlaying}, CurrentAnimation: '{cachedSpriteAnimator.CurrentAnimation}'");
            }
#endif
        }
        
        /// <summary>
        /// Timer-based animation completion fallback due to SpriteAnimator bug
        /// </summary>
        private System.Collections.IEnumerator AnimationCompletionTimer(float duration)
        {
            yield return new WaitForSeconds(duration);
            
            // Only complete if we haven't been cleaned up already
            if (currentAttacker != null && attackCompletionCallback != null)
            {
#if UNITY_EDITOR
                if (enableDebugLogs)
                    Debug.Log($"[RangedAttack] {currentAttacker.name}: Timer-based animation completion triggered after {duration}s");
#endif
                
                // If no frame event fired the attack, fire it now (fallback)
                if (!projectileFired && currentAttacker != null && currentTarget != null)
                {
#if UNITY_EDITOR
                    if (enableDebugLogs)
                        Debug.Log($"[RangedAttack] {currentAttacker.name}: No frame event detected - using timer fallback attack");
#endif
                    SpawnAttack(currentAttacker, currentTarget);
                }
                
#if UNITY_EDITOR
                if (enableDebugLogs)
                    Debug.Log($"[RangedAttack] {currentAttacker.name}: Attack cooldown remaining: {CooldownRemaining:F1}s");
#endif
                
                // Immediately unsubscribe to prevent multiple calls
                if (cachedSpriteAnimator != null)
                {
                    cachedSpriteAnimator.OnAnimationEvent -= OnAnimationFrameEvent;
                    cachedSpriteAnimator.OnAnimationCompleted -= OnAnimationCompleted;
                    
                    // Clear temporary speed override
                    cachedSpriteAnimator.ClearTemporarySpeedMultiplier();
                }
                
                // Re-enable EnemyAnimationController
                if (currentAttacker != null)
                {
                    if (cachedAnimationController != null)
                    {
                        cachedAnimationController.enabled = true;
#if UNITY_EDITOR
                        if (enableDebugLogs)
                            Debug.Log($"[RangedAttack] {currentAttacker.name}: Re-enabled EnemyAnimationController via timer");
#endif
                    }
                }
                
                // Notify completion and cleanup
                attackCompletionCallback?.Invoke();
                CleanupAttackState();
            }
        }
    }