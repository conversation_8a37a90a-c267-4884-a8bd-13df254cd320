using UnityEngine;
using Sirenix.OdinInspector;

/// <summary>
/// Specialized test script for investigating fork projectile status effect issues
/// </summary>
public class ForkProjectileStatusEffectTest : MonoBehaviour
{
    [Title("Test Configuration")]
    [SerializeField] private GameObject projectilePrefab;
    [SerializeField] private GameObject targetPrefab;
    
    [Range(0f, 100f)]
    [SerializeField] private float testAilmentChance = 100f;
    
    [SerializeField] private DamageType testDamageType = DamageType.Fire;
    
    [Title("Fork Settings")]
    [SerializeField] private bool enableFork = true;
    [SerializeField] private int forkCount = 2;
    [SerializeField] private float forkAngle = 30f;
    
    [Title("Test Results")]
    [ShowInInspector, ReadOnly]
    private int originalHitTests = 0;
    [ShowInInspector, ReadOnly]
    private int originalHitSuccesses = 0;
    [ShowInInspector, ReadOnly]
    private float originalHitSuccessRate = 0f;
    
    [ShowInInspector, <PERSON>Only]
    private int forkHitTests = 0;
    [ShowInInspector, ReadOnly]
    private int forkHitSuccesses = 0;
    [ShowInInspector, ReadOnly]
    private float forkHitSuccessRate = 0f;
    
    [Header("Debug Settings")]
    [SerializeField] private bool enableDebugLogging = false;
    
    private void Start()
    {
        // Enable fork debug logging in the projectile system
        if (enableDebugLogging)
        {
            StatusEffectHelper.SetForkDebugLogging(true);
        }
    }
    
    [Button("Run Fork Status Effect Test")]
    public void RunForkStatusEffectTest()
    {
        if (projectilePrefab == null || targetPrefab == null)
        {
            Debug.LogError("Please assign projectile and target prefabs!");
            return;
        }
        
        Debug.Log("<color=yellow>=== Fork Projectile Status Effect Test ===</color>");
        Debug.Log($"Testing with {testAilmentChance}% ailment chance, {testDamageType} damage type");
        Debug.Log($"Fork enabled: {enableFork}, Fork count: {forkCount}, Fork angle: {forkAngle}°");
        
        // Reset counters
        originalHitTests = 0;
        originalHitSuccesses = 0;
        forkHitTests = 0;
        forkHitSuccesses = 0;
        
        // Run multiple test iterations
        for (int i = 0; i < 10; i++)
        {
            RunSingleTest(i + 1);
        }
        
        // Calculate success rates
        originalHitSuccessRate = originalHitTests > 0 ? (float)originalHitSuccesses / originalHitTests * 100f : 0f;
        forkHitSuccessRate = forkHitTests > 0 ? (float)forkHitSuccesses / forkHitTests * 100f : 0f;
        
        // Display results
        Debug.Log("\n<color=cyan>=== Test Results ===</color>");
        Debug.Log($"Original Hit Success Rate: {originalHitSuccessRate:F1}% ({originalHitSuccesses}/{originalHitTests})");
        Debug.Log($"Fork Hit Success Rate: {forkHitSuccessRate:F1}% ({forkHitSuccesses}/{forkHitTests})");
        Debug.Log($"Expected Success Rate: {testAilmentChance:F1}%");
        
        // Analysis
        float originalDifference = Mathf.Abs(originalHitSuccessRate - testAilmentChance);
        float forkDifference = Mathf.Abs(forkHitSuccessRate - testAilmentChance);
        
        string originalColor = originalDifference <= 20f ? "green" : "red";
        string forkColor = forkDifference <= 20f ? "green" : "red";
        
        Debug.Log($"\n<color={originalColor}>Original Hit Analysis: {(originalDifference <= 20f ? "PASS" : "FAIL")} (Difference: {originalDifference:F1}%)</color>");
        Debug.Log($"<color={forkColor}>Fork Hit Analysis: {(forkDifference <= 20f ? "PASS" : "FAIL")} (Difference: {forkDifference:F1}%)</color>");
        
        if (forkDifference > 20f)
        {
            Debug.LogError("<color=red>Fork projectiles are failing to apply status effects consistently!</color>");
        }
    }
    
    private void RunSingleTest(int testNumber)
    {
        Debug.Log($"\n--- Test {testNumber} ---");
        
        // Create target with status effect manager
        GameObject target = Instantiate(targetPrefab, Vector3.right * 2f, Quaternion.identity);
        StatusEffectManager statusManager = target.GetComponent<StatusEffectManager>();
        if (statusManager == null)
        {
            statusManager = target.AddComponent<StatusEffectManager>();
        }
        
        // Create projectile
        GameObject projectile = Instantiate(projectilePrefab, Vector3.zero, Quaternion.identity);
        Projectile projectileComponent = projectile.GetComponent<Projectile>();
        
        if (projectileComponent == null)
        {
            Debug.LogError("Projectile prefab doesn't have Projectile component!");
            DestroyImmediate(target);
            DestroyImmediate(projectile);
            return;
        }
        
        // Configure projectile
        projectileComponent.damageType = testDamageType;
        projectileComponent.ailmentChance = testAilmentChance;
        
        if (enableFork)
        {
            projectileComponent.SetFork(true, forkCount, forkAngle);
        }
        
        // Initialize projectile to move toward target
        Vector2 direction = (target.transform.position - projectile.transform.position).normalized;
        projectileComponent.Initialize(projectile.transform.position, direction, 10f);
        
        // Wait for projectile to hit and potentially fork
        StartCoroutine(MonitorTest(target, projectile, testNumber));
    }
    
    private System.Collections.IEnumerator MonitorTest(GameObject target, GameObject projectile, int testNumber)
    {
        StatusEffectManager statusManager = target.GetComponent<StatusEffectManager>();
        float timeout = 5f;
        float elapsed = 0f;
        
        int initialEffectCount = CountActiveEffects(statusManager);
        bool originalHitDetected = false;
        bool forkHitsDetected = false;
        
        while (elapsed < timeout)
        {
            elapsed += Time.deltaTime;
            
            int currentEffectCount = CountActiveEffects(statusManager);
            
            // Check if effects were applied (indicating hits occurred)
            if (currentEffectCount > initialEffectCount && !originalHitDetected)
            {
                originalHitDetected = true;
                originalHitTests++;
                originalHitSuccesses++;
                
                if (enableDebugLogging)
                {
                    Debug.Log($"[Test {testNumber}] Original hit detected - status effect applied");
                }
            }
            
            // For fork projectiles, we expect potentially multiple hits
            if (enableFork && currentEffectCount > initialEffectCount + 1 && !forkHitsDetected)
            {
                forkHitsDetected = true;
                forkHitTests++;
                forkHitSuccesses++;
                
                if (enableDebugLogging)
                {
                    Debug.Log($"[Test {testNumber}] Fork hits detected - additional status effects applied");
                }
            }
            
            yield return null;
        }
        
        // If no effects were applied, count as failures
        if (!originalHitDetected)
        {
            originalHitTests++;
            if (enableDebugLogging)
            {
                Debug.Log($"[Test {testNumber}] Original hit failed - no status effect applied");
            }
        }
        
        if (enableFork && !forkHitsDetected)
        {
            forkHitTests++;
            if (enableDebugLogging)
            {
                Debug.Log($"[Test {testNumber}] Fork hits failed - no additional status effects applied");
            }
        }
        
        // Cleanup
        if (target != null) DestroyImmediate(target);
        if (projectile != null) DestroyImmediate(projectile);
    }
    
    private int CountActiveEffects(StatusEffectManager statusManager)
    {
        if (statusManager == null) return 0;
        
        // Use reflection to access private activeEffects field
        var field = typeof(StatusEffectManager).GetField("activeEffects", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (field != null && field.GetValue(statusManager) is System.Collections.IList list)
        {
            return list.Count;
        }
        
        return 0;
    }
    
    [Button("Enable Debug Logging")]
    public void EnableDebugLogging()
    {
        enableDebugLogging = true;
        StatusEffectHelper.SetForkDebugLogging(true);
        Debug.Log("Fork debug logging enabled");
    }
    
    [Button("Disable Debug Logging")]
    public void DisableDebugLogging()
    {
        enableDebugLogging = false;
        StatusEffectHelper.SetForkDebugLogging(false);
        Debug.Log("Fork debug logging disabled");
    }
}
