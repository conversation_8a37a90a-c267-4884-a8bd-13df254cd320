using UnityEngine;

/// <summary>
/// Centralized helper class for applying status effects based on damage type and ailment chance.
/// Eliminates code duplication between PlayerStats and HealthComponent damage handling.
/// </summary>
public static class StatusEffectHelper
{
    /// <summary>
    /// Configuration for status effect parameters
    /// </summary>
    [System.Serializable]
    public struct StatusEffectConfig
    {
        // Ignite (Fire) parameters
        public float ignitePercent;          // Percentage of damage dealt as DoT
        public float igniteDuration;         // Duration in seconds
        public float igniteTickInterval;     // Time between damage ticks
        
        // Freeze (Ice) parameters  
        public float freezeSlowAmount;       // Movement speed reduction (0-1)
        public float freezeDuration;         // Duration in seconds
        
        // Bleed (Physical) parameters
        public float bleedPercent;           // Percentage of damage dealt as DoT
        public float bleedDuration;          // Duration in seconds
        public float bleedTickInterval;      // Time between damage ticks
        
        // Shock (Lightning) parameters
        public float shockChainDamage;       // Damage for chain lightning
        public float shockChainRange;        // Range for chain lightning
        public float shockDuration;          // Duration in seconds
        
        /// <summary>
        /// Default configuration with balanced values
        /// </summary>
        public static StatusEffectConfig Default => new StatusEffectConfig
        {
            ignitePercent = 0.2f,           // 20% of damage over time
            igniteDuration = 4f,            // 4 seconds
            igniteTickInterval = 0.5f,      // Every 0.5 seconds (8 ticks total)
            
            freezeSlowAmount = 0.5f,        // 50% movement speed reduction
            freezeDuration = 2f,            // 2 seconds
            
            bleedPercent = 0.15f,           // 15% of damage over time
            bleedDuration = 6f,             // 6 seconds
            bleedTickInterval = 1f,         // Every 1 second (6 ticks total)
            
            shockChainDamage = 0.1f,        // 10% of original damage for chains
            shockChainRange = 3f,           // 3 unit range for chains
            shockDuration = 2f              // 2 seconds
        };
    }
    
    /// <summary>
    /// Try to apply status effect based on damage type and ailment chance.
    /// Uses gem data from DamageInfo if available, otherwise uses default configuration.
    /// </summary>
    /// <param name="damageInfo">Damage information including type, ailment chance, and optional gem data</param>
    /// <param name="finalDamage">Final damage amount after all calculations</param>
    /// <param name="statusEffectManager">Target's status effect manager</param>
    public static void TryApplyAilment(DamageInfo damageInfo, float finalDamage, StatusEffectManager statusEffectManager)
    {
        // Use gem data if available, otherwise use default configuration
        if (damageInfo.skillGemData != null)
        {
            TryApplyAilment(damageInfo, finalDamage, statusEffectManager, damageInfo.skillGemData, damageInfo.supportGems);
        }
        else
        {
            TryApplyAilment(damageInfo, finalDamage, statusEffectManager, StatusEffectConfig.Default);
        }
    }
    
    /// <summary>
    /// Try to apply status effect based on damage type and ailment chance.
    /// Uses gem-based configuration with support gem modifiers applied.
    /// </summary>
    /// <param name="damageInfo">Damage information including type and ailment chance</param>
    /// <param name="finalDamage">Final damage amount after all calculations</param>
    /// <param name="statusEffectManager">Target's status effect manager</param>
    /// <param name="skillGemData">Skill gem data containing base status effect parameters</param>
    /// <param name="supportGems">List of support gems that may modify status effects</param>
    public static void TryApplyAilment(DamageInfo damageInfo, float finalDamage, StatusEffectManager statusEffectManager,
        SkillGemData skillGemData, System.Collections.Generic.List<GemInstance> supportGems = null)
    {
        if (skillGemData == null)
        {
            // Fallback to default configuration
            TryApplyAilment(damageInfo, finalDamage, statusEffectManager, StatusEffectConfig.Default);
            return;
        }

        // Get base configuration from skill gem
        StatusEffectConfig config = skillGemData.GetStatusEffectConfig();

        // Apply support gem modifiers
        if (supportGems != null)
        {
            foreach (var supportGem in supportGems)
            {
                if (supportGem?.gemDataTemplate is SupportGemData supportData)
                {
                    config = supportData.ApplyStatusEffectModifiers(config);
                }
            }
        }

        // Apply the status effect with the final configuration
        TryApplyAilment(damageInfo, finalDamage, statusEffectManager, config);
    }

    /// <summary>
    /// Try to apply status effect based on damage type and ailment chance.
    /// Uses provided configuration values for customizable effect parameters.
    /// </summary>
    /// <param name="damageInfo">Damage information including type and ailment chance</param>
    /// <param name="finalDamage">Final damage amount after all calculations</param>
    /// <param name="statusEffectManager">Target's status effect manager</param>
    /// <param name="config">Configuration for status effect parameters</param>
    public static void TryApplyAilment(DamageInfo damageInfo, float finalDamage, StatusEffectManager statusEffectManager, StatusEffectConfig config)
    {
        if (damageInfo.ailmentChance <= 0 || statusEffectManager == null) return;
        
        // Roll for ailment chance (Random.value is 0.0 to 1.0 inclusive)
        // If random roll is higher than the chance percentage, don't apply effect
        if (Random.value > damageInfo.ailmentChance / 100f) return;
        
        switch (damageInfo.type)
        {
            case DamageType.Fire:
                ApplyIgniteEffect(finalDamage, damageInfo.source, statusEffectManager, config);
                break;
                
            case DamageType.Ice:
                ApplyFreezeEffect(damageInfo.source, statusEffectManager, config);
                break;
                
            case DamageType.Physical:
                ApplyBleedEffect(finalDamage, damageInfo.source, statusEffectManager, config);
                break;
                
            case DamageType.Lightning:
                ApplyShockEffect(finalDamage, damageInfo.source, statusEffectManager, config);
                break;
        }
    }
    
    private static void ApplyIgniteEffect(float finalDamage, string source, StatusEffectManager statusEffectManager, StatusEffectConfig config)
    {
        float totalIgniteDamage = finalDamage * config.ignitePercent;
        float tickCount = config.igniteDuration / config.igniteTickInterval;
        float damagePerTick = totalIgniteDamage / tickCount;
        
        var igniteEffect = new IgniteEffect(damagePerTick, config.igniteDuration, source);
        statusEffectManager.ApplyStatusEffect(igniteEffect);
        
       // Debug.Log($"Applied ignite: {damagePerTick:F1} damage per tick for {config.igniteDuration}s");
    }
    
    private static void ApplyFreezeEffect(string source, StatusEffectManager statusEffectManager, StatusEffectConfig config)
    {
        var freezeEffect = new FreezeEffect(config.freezeSlowAmount, config.freezeDuration, source);
        statusEffectManager.ApplyStatusEffect(freezeEffect);
        
       // Debug.Log($"Applied freeze: {config.freezeSlowAmount * 100}% slow for {config.freezeDuration}s");
    }
    
    private static void ApplyBleedEffect(float finalDamage, string source, StatusEffectManager statusEffectManager, StatusEffectConfig config)
    {
        float totalBleedDamage = finalDamage * config.bleedPercent;
        float tickCount = config.bleedDuration / config.bleedTickInterval;
        float damagePerTick = totalBleedDamage / tickCount;
        
        var bleedEffect = new BleedEffect(damagePerTick, config.bleedDuration, source);
        statusEffectManager.ApplyStatusEffect(bleedEffect);
        
       // Debug.Log($"Applied bleed: {damagePerTick:F1} damage per tick for {config.bleedDuration}s");
    }
    
    private static void ApplyShockEffect(float finalDamage, string source, StatusEffectManager statusEffectManager, StatusEffectConfig config)
    {
        float chainDamage = finalDamage * config.shockChainDamage;
        
        var shockEffect = new ShockEffect(chainDamage, config.shockChainRange, config.shockDuration, source);
        statusEffectManager.ApplyStatusEffect(shockEffect);
        
        //Debug.Log($"Applied shock: {chainDamage:F1} chain damage, {config.shockChainRange}m range for {config.shockDuration}s");
    }
}
