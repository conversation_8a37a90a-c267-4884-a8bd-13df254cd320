using UnityEngine;
using System.Collections.Generic;
using Sirenix.OdinInspector;

/// <summary>
/// Configuration for chunk-based buffs
/// </summary>
[System.Serializable]
public class ChunkBuffConfiguration
{
    [Title("Distance Scaling")]
    [Tooltip("How many chunks away from (0,0,0) before scaling starts")]
    [MinValue(0)]
    public int baseDistanceThreshold = 3;
    
    [Tooltip("Damage bonus per distance unit (percentage)")]
    [MinValue(0f)]
    public float damagePerDistance = 5f; // 5% per distance
    
    [Tooltip("Crit chance bonus per distance unit (percentage)")]
    [MinValue(0f)]
    public float critChancePerDistance = 2f; // 2% per distance
    
    [Tooltip("Maximum distance scaling bonus")]
    [MinValue(1)]
    public int maxScalingDistance = 20;
    
    [Title("Random Chunk Buffs")]
    [Tooltip("Chance for a chunk to have random buffs")]
    [Range(0f, 1f)]
    public float randomBuffChance = 0.3f;
    
    [Tooltip("Possible random buffs for chunks")]
    public RandomChunkBuff[] possibleRandomBuffs;
    
    [Title("Special Chunk Rules")]
    [Tooltip("Distance intervals for special buff zones")]
    [MinValue(1)]
    public int eliteZoneInterval = 10; // Every 10 chunks = elite zone
    
    [Tooltip("Elite zone damage bonus (percentage)")]
    public float eliteZoneDamageBonus = 25f;
    
    [Tooltip("Elite zone projectile count bonus")]
    public int eliteZoneProjectileBonus = 1;
}

/// <summary>
/// Random buff that can be applied to chunks
/// </summary>
[System.Serializable]
public class RandomChunkBuff
{
    [HorizontalGroup("Buff")]
    [LabelWidth(60)]
    public string name;
    
    [HorizontalGroup("Buff")]
    [LabelWidth(50)]
    [Range(0f, 1f)]
    public float weight = 1f;
    
    [Title("Modifiers")]
    public AttackModifier[] modifiers;
    
    [TextArea(2, 3)]
    public string description;
}

/// <summary>
/// Buffs applied to a specific chunk
/// </summary>
public class ChunkBuffData
{
    public Vector3Int chunkPosition;
    public List<AttackModifier> distanceModifiers = new List<AttackModifier>();
    public List<AttackModifier> randomModifiers = new List<AttackModifier>();
    public List<AttackModifier> specialModifiers = new List<AttackModifier>();
    public bool isEliteZone;
    public string buffDescription;
    
    // Cached combined modifiers to avoid allocations
    public List<AttackModifier> cachedCombinedModifiers;
    private bool isDirty = true;
    
    public List<AttackModifier> GetAllModifiers()
    {
        // Return cached list if available and not dirty
        if (!isDirty && cachedCombinedModifiers != null)
        {
            return cachedCombinedModifiers;
        }
        
        // Initialize or clear cached list
        if (cachedCombinedModifiers == null)
        {
            cachedCombinedModifiers = new List<AttackModifier>(16);
        }
        else
        {
            cachedCombinedModifiers.Clear();
        }
        
        // Combine all modifiers
        cachedCombinedModifiers.AddRange(distanceModifiers);
        cachedCombinedModifiers.AddRange(randomModifiers);
        cachedCombinedModifiers.AddRange(specialModifiers);
        
        isDirty = false;
        return cachedCombinedModifiers;
    }
    
    public void MarkDirty()
    {
        isDirty = true;
    }
}

/// <summary>
/// Manages chunk-based buffs and distance scaling for enemy attacks
/// </summary>
public class ChunkBuffSystem : MonoBehaviour
{
    [Title("Configuration")]
    [SerializeField] private ChunkBuffConfig configAsset; // Assign this in the Inspector
    [HideInInspector]
    [SerializeField] private ChunkBuffConfiguration configuration = new ChunkBuffConfiguration();
    
    // Shared StringBuilder for description generation
    private static System.Text.StringBuilder sharedStringBuilder = new System.Text.StringBuilder(BuffConstants.STRING_BUILDER_CAPACITY);
    
    [Title("Debug")]
    [SerializeField] private bool enableDebugLogging = false;
    [SerializeField] private bool showChunkBuffsInInspector = false;
    
    [ShowIf("showChunkBuffsInInspector")]
    [ReadOnly]
    [SerializeField] private List<ChunkBuffData> activeChunkBuffs = new List<ChunkBuffData>();
    
    [SerializeField, MinValue(1), Tooltip("Size of a chunk in world units (must match TilemapChunkManager)")]
    private int chunkSize = 32;
    
    // Static instance for global access
    public static ChunkBuffSystem Instance { get; private set; }
    
    // Cache for chunk buffs with LRU eviction
    private Dictionary<Vector3Int, ChunkBuffData> chunkBuffCache = new Dictionary<Vector3Int, ChunkBuffData>();
    private LinkedList<Vector3Int> cacheOrder = new LinkedList<Vector3Int>();
    private Dictionary<Vector3Int, LinkedListNode<Vector3Int>> cacheNodes = new Dictionary<Vector3Int, LinkedListNode<Vector3Int>>();
    
    // Cache cleanup timing
    private float lastCacheCleanupTime = 0f;
    
    // Configuration access
    public ChunkBuffConfiguration Configuration => configuration;
    
    // Static access to chunk size for other systems
    public static int ChunkSize { get; private set; } = 32;
    
    // Flag to avoid spamming warnings
    private static bool hasShownMissingChunkManagerWarning = false;
    
    private void Awake()
    {
        // Singleton pattern
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);

            // Store chunk size globally
            ChunkSize = chunkSize;

            // Load runtime configuration from asset if provided
            if (configAsset != null)
            {
                configuration = configAsset.ToRuntimeConfiguration();
            }
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    /// <summary>
    /// Get all attack modifiers for a specific chunk position
    /// </summary>
    public List<AttackModifier> GetChunkModifiers(Vector3Int chunkPosition)
    {
        // Periodic cache cleanup
        if (Time.time - lastCacheCleanupTime > BuffConstants.CACHE_CLEANUP_INTERVAL)
        {
            PerformCacheCleanup();
            lastCacheCleanupTime = Time.time;
        }
        
        // Check cache first
        if (chunkBuffCache.TryGetValue(chunkPosition, out ChunkBuffData cachedData))
        {
            // Update LRU order
            UpdateCacheAccess(chunkPosition);
            return cachedData.GetAllModifiers();
        }
        
        // Generate new chunk buff data
        ChunkBuffData chunkData = GenerateChunkBuffs(chunkPosition);
        
        // Add to cache with LRU tracking
        AddToCache(chunkPosition, chunkData);
        
        if (showChunkBuffsInInspector && !activeChunkBuffs.Contains(chunkData))
        {
            activeChunkBuffs.Add(chunkData);
        }
        
        if (enableDebugLogging)
        {
            Debug.Log($"Generated buffs for chunk {chunkPosition}: {chunkData.buffDescription}");
        }
        
        return chunkData.GetAllModifiers();
    }
    
    /// <summary>
    /// Generate buff data for a specific chunk
    /// </summary>
    private ChunkBuffData GenerateChunkBuffs(Vector3Int chunkPosition)
    {
        ChunkBuffData chunkData = BuffSystemPools.GetChunkBuffData();
        chunkData.chunkPosition = chunkPosition;
        
        // Calculate distance from spawn (0,0,0)
        float distance = Mathf.Sqrt(chunkPosition.x * chunkPosition.x + chunkPosition.y * chunkPosition.y);
        int scalingDistance = Mathf.Max(0, Mathf.RoundToInt(distance) - configuration.baseDistanceThreshold);
        scalingDistance = Mathf.Min(scalingDistance, configuration.maxScalingDistance);
        
        // Apply distance-based scaling
        if (scalingDistance > 0)
        {
            ApplyDistanceScaling(chunkData, scalingDistance);
        }
        
        // Check for elite zones
        if (IsEliteZone(chunkPosition))
        {
            ApplyEliteZoneBuffs(chunkData);
        }
        
        // Apply random buffs
        if (Random.value < configuration.randomBuffChance)
        {
            ApplyRandomBuffs(chunkData);
        }
        
        // Generate description
        GenerateBuffDescription(chunkData, scalingDistance);
        
        return chunkData;
    }
    
    /// <summary>
    /// Apply distance-based scaling modifiers
    /// </summary>
    private void ApplyDistanceScaling(ChunkBuffData chunkData, int scalingDistance)
    {
        float distanceMultiplier = scalingDistance;
        
        // Damage scaling
        if (configuration.damagePerDistance > 0f)
        {
            chunkData.distanceModifiers.Add(new AttackModifier(
                AttackModifierType.DamageMultiplier,
                configuration.damagePerDistance * distanceMultiplier,
                BuffConstants.SOURCE_DISTANCE
            ));
        }
        
        // Crit chance scaling
        if (configuration.critChancePerDistance > 0f)
        {
            chunkData.distanceModifiers.Add(new AttackModifier(
                AttackModifierType.CritChance,
                configuration.critChancePerDistance * distanceMultiplier,
                BuffConstants.SOURCE_DISTANCE
            ));
        }
    }
    
    /// <summary>
    /// Check if chunk is in an elite zone
    /// </summary>
    private bool IsEliteZone(Vector3Int chunkPosition)
    {
        if (configuration.eliteZoneInterval <= 0) return false;
        
        float distance = Mathf.Sqrt(chunkPosition.x * chunkPosition.x + chunkPosition.y * chunkPosition.y);
        return Mathf.RoundToInt(distance) % configuration.eliteZoneInterval == 0 && distance > 0;
    }
    
    /// <summary>
    /// Apply elite zone buffs
    /// </summary>
    private void ApplyEliteZoneBuffs(ChunkBuffData chunkData)
    {
        chunkData.isEliteZone = true;
        
        // Elite damage bonus
        if (configuration.eliteZoneDamageBonus > 0f)
        {
            chunkData.specialModifiers.Add(new AttackModifier(
                AttackModifierType.DamageMultiplier,
                configuration.eliteZoneDamageBonus,
                BuffConstants.SOURCE_ELITE
            ));
        }
        
        // Elite projectile bonus
        if (configuration.eliteZoneProjectileBonus > 0)
        {
            chunkData.specialModifiers.Add(new AttackModifier(
                AttackModifierType.ProjectileCount,
                configuration.eliteZoneProjectileBonus,
                BuffConstants.SOURCE_ELITE
            ));
        }
    }
    
    /// <summary>
    /// Apply random buffs to chunk
    /// </summary>
    private void ApplyRandomBuffs(ChunkBuffData chunkData)
    {
        if (configuration.possibleRandomBuffs == null || configuration.possibleRandomBuffs.Length == 0)
            return;
        
        // Select random buff based on weights
        RandomChunkBuff selectedBuff = SelectRandomBuff();
        if (selectedBuff != null)
        {
            foreach (var modifier in selectedBuff.modifiers)
            {
                var randomModifier = new AttackModifier(
                    modifier.type,
                    modifier.value,
                    BuffConstants.SOURCE_RANDOM_PREFIX + selectedBuff.name
                );
                chunkData.randomModifiers.Add(randomModifier);
            }
        }
    }
    
    /// <summary>
    /// Select a random buff based on weights
    /// </summary>
    private RandomChunkBuff SelectRandomBuff()
    {
        float totalWeight = 0f;
        foreach (var buff in configuration.possibleRandomBuffs)
        {
            totalWeight += buff.weight;
        }
        
        if (totalWeight <= 0f) return null;
        
        float randomValue = Random.value * totalWeight;
        float currentWeight = 0f;
        
        foreach (var buff in configuration.possibleRandomBuffs)
        {
            currentWeight += buff.weight;
            if (randomValue <= currentWeight)
            {
                return buff;
            }
        }
        
        return configuration.possibleRandomBuffs[configuration.possibleRandomBuffs.Length - 1];
    }
    
    /// <summary>
    /// Generate human-readable description of chunk buffs
    /// </summary>
    private void GenerateBuffDescription(ChunkBuffData chunkData, int scalingDistance)
    {
        // Clear and reuse shared StringBuilder
        sharedStringBuilder.Clear();
        
        if (scalingDistance > 0)
        {
            sharedStringBuilder.Append("Distance +");
            sharedStringBuilder.Append(scalingDistance);
            sharedStringBuilder.Append(' ');
        }
        
        if (chunkData.isEliteZone)
        {
            sharedStringBuilder.Append("[ELITE ZONE] ");
        }
        
        if (chunkData.randomModifiers.Count > 0)
        {
            sharedStringBuilder.Append("[RANDOM BUFF] ");
        }
        
        chunkData.buffDescription = sharedStringBuilder.ToString().Trim();
        if (string.IsNullOrEmpty(chunkData.buffDescription))
        {
            chunkData.buffDescription = "No special buffs";
        }
    }
    
    /// <summary>
    /// Clear cached chunk data (useful for testing or when configuration changes)
    /// </summary>
    [Button("Clear Chunk Cache")]
    public void ClearChunkCache()
    {
        // Return all ChunkBuffData to pool before clearing
        foreach (var kvp in chunkBuffCache)
        {
            BuffSystemPools.ReturnChunkBuffData(kvp.Value);
        }
        
        chunkBuffCache.Clear();
        cacheOrder.Clear();
        cacheNodes.Clear();
        activeChunkBuffs.Clear();
        
        if (enableDebugLogging)
        {
            Debug.Log("Chunk buff cache cleared");
        }
    }
    
    /// <summary>
    /// Update LRU cache access order
    /// </summary>
    private void UpdateCacheAccess(Vector3Int chunkPosition)
    {
        if (cacheNodes.TryGetValue(chunkPosition, out var node))
        {
            // Move to front (most recently used)
            cacheOrder.Remove(node);
            cacheOrder.AddFirst(node);
        }
    }
    
    /// <summary>
    /// Add new chunk to cache with LRU tracking
    /// </summary>
    private void AddToCache(Vector3Int chunkPosition, ChunkBuffData chunkData)
    {
        // Check if we need to evict old entries
        if (chunkBuffCache.Count >= BuffConstants.MAX_CHUNK_CACHE_SIZE)
        {
            // Remove least recently used
            var lruChunk = cacheOrder.Last.Value;
            cacheOrder.RemoveLast();
            cacheNodes.Remove(lruChunk);
            
            // Return old data to pool
            if (chunkBuffCache.TryGetValue(lruChunk, out var oldData))
            {
                BuffSystemPools.ReturnChunkBuffData(oldData);
                chunkBuffCache.Remove(lruChunk);
                
                // Remove from inspector list if present
                if (showChunkBuffsInInspector)
                {
                    activeChunkBuffs.Remove(oldData);
                }
            }
        }
        
        // Add new entry
        chunkBuffCache[chunkPosition] = chunkData;
        var newNode = cacheOrder.AddFirst(chunkPosition);
        cacheNodes[chunkPosition] = newNode;
    }
    
    /// <summary>
    /// Perform periodic cache cleanup
    /// </summary>
    private void PerformCacheCleanup()
    {
        if (PlayerManager.PlayerTransform == null) return;
        
        Vector3 playerPos = PlayerManager.PlayerTransform.position;
        Vector3Int playerChunk = WorldToChunkPosition(playerPos);
        
        var chunksToRemove = new List<Vector3Int>();
        
        foreach (var kvp in chunkBuffCache)
        {
            float distance = Vector3Int.Distance(kvp.Key, playerChunk);
            if (distance > BuffConstants.MAX_CACHE_DISTANCE)
            {
                chunksToRemove.Add(kvp.Key);
            }
        }
        
        foreach (var chunk in chunksToRemove)
        {
            if (cacheNodes.TryGetValue(chunk, out var node))
            {
                cacheOrder.Remove(node);
                cacheNodes.Remove(chunk);
            }
            
            if (chunkBuffCache.TryGetValue(chunk, out var data))
            {
                BuffSystemPools.ReturnChunkBuffData(data);
                chunkBuffCache.Remove(chunk);
                
                if (showChunkBuffsInInspector)
                {
                    activeChunkBuffs.Remove(data);
                }
            }
        }
        
        if (enableDebugLogging && chunksToRemove.Count > 0)
        {
            Debug.Log($"[ChunkBuffSystem] Cleaned up {chunksToRemove.Count} distant chunks from cache");
        }
    }
    
    /// <summary>
    /// Get world position from chunk coordinates
    /// </summary>
    public static Vector3Int WorldToChunkPosition(Vector3 worldPosition, float overrideChunkSize = -1f)
    {
        int width, height;
        if (TilemapChunkManager.Instance != null)
        {
            width = TilemapChunkManager.Instance.GetChunkWidth();
            height = TilemapChunkManager.Instance.GetChunkHeight();
        }
        else
        {
            if (!hasShownMissingChunkManagerWarning)
            {
#if UNITY_EDITOR
                Debug.LogWarning("[ChunkBuffSystem] TilemapChunkManager.Instance not found – using fallback chunkSize. Ensure a TilemapChunkManager is present in the scene for accurate chunk calculations.");
#endif
                hasShownMissingChunkManagerWarning = true;
            }
            int size = overrideChunkSize > 0f ? Mathf.RoundToInt(overrideChunkSize) : ChunkSize;
            width = size;
            height = size;
        }
        return new Vector3Int(
            Mathf.FloorToInt(worldPosition.x / width),
            Mathf.FloorToInt(worldPosition.y / height),
            0
        );
    }
    
    #if UNITY_EDITOR
    [Button("Test Chunk at Position")]
    public void TestChunkAtPosition(Vector3 worldPosition)
    {
        Vector3Int chunkPos = WorldToChunkPosition(worldPosition);
        List<AttackModifier> modifiers = GetChunkModifiers(chunkPos);

        if (enableDebugLogging)
        {
            Debug.Log($"Chunk at {worldPosition} (chunk {chunkPos}) has {modifiers.Count} modifiers:");
            foreach (var modifier in modifiers)
            {
                Debug.Log($"- {modifier.type}: {modifier.value} (from {modifier.source})");
            }
        }
    }
    
    [Button("Generate Random Buffs Preview")]
    public void GenerateRandomBuffsPreview()
    {
        if (configuration.possibleRandomBuffs == null || configuration.possibleRandomBuffs.Length == 0)
        {
            if (enableDebugLogging)
            {
                Debug.Log("No random buffs configured");
            }
            return;
        }

        if (enableDebugLogging)
        {
            Debug.Log("Random Buff Distribution:");
            for (int i = 0; i < 10; i++)
            {
                var buff = SelectRandomBuff();
                Debug.Log($"Roll {i+1}: {buff?.name ?? "None"}");
            }
        }
    }

    private void OnValidate()
    {
        // Keep runtime configuration in sync in editor
        if (configAsset != null)
        {
            configuration = configAsset.ToRuntimeConfiguration();
        }
    }
    #endif
} 