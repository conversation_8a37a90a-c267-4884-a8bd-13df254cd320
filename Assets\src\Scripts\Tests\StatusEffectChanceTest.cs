using UnityEngine;
using Sirenix.OdinInspector;

/// <summary>
/// Test script to verify status effect chance logic works correctly
/// </summary>
public class StatusEffectChanceTest : MonoBehaviour
{
    [Title("Test Configuration")]
    [Range(0f, 100f)]
    [SerializeField] private float testAilmentChance = 100f;
    
    [SerializeField] private DamageType testDamageType = DamageType.Fire;
    
    [Title("Test Results")]
    [ShowInInspector, ReadOnly]
    private int totalTests = 0;
    
    [ShowInInspector, ReadOnly]
    private int successfulApplications = 0;
    
    [ShowInInspector, ReadOnly]
    private float actualSuccessRate = 0f;
    
    [Header("Debug Settings")]
    [SerializeField] private bool enableDebugLogging = false;
    
    private StatusEffectManager testStatusEffectManager;
    
    private void Awake()
    {
        // Create a test status effect manager
        testStatusEffectManager = gameObject.AddComponent<StatusEffectManager>();
    }
    
    [Button("Test Status Effect Chance (100 iterations)")]
    public void TestStatusEffectChance()
    {
        TestStatusEffectChance(100);
    }
    
    [Button("Test Status Effect Chance (1000 iterations)")]
    public void TestStatusEffectChanceLarge()
    {
        TestStatusEffectChance(1000);
    }
    
    private void TestStatusEffectChance(int iterations)
    {
        totalTests = 0;
        successfulApplications = 0;
        
        for (int i = 0; i < iterations; i++)
        {
            totalTests++;
            
            // Create test damage info
            DamageInfo damageInfo = new DamageInfo(
                10f, // damage amount
                testDamageType,
                false, // not critical
                1f, // crit multiplier
                "Test", // source
                testAilmentChance // ailment chance
            );
            
            // Count current active effects before test
            int effectsBeforeTest = CountActiveEffects();
            
            // Try to apply ailment
            StatusEffectHelper.TryApplyAilment(damageInfo, 10f, testStatusEffectManager);
            
            // Count effects after test
            int effectsAfterTest = CountActiveEffects();
            
            // If more effects were added, the application was successful
            if (effectsAfterTest > effectsBeforeTest)
            {
                successfulApplications++;
                
                if (enableDebugLogging)
                {
                    Debug.Log($"Test {i + 1}: Status effect applied successfully");
                }
            }
            else if (enableDebugLogging)
            {
                Debug.Log($"Test {i + 1}: Status effect was not applied");
            }
        }
        
        actualSuccessRate = (float)successfulApplications / totalTests * 100f;
        
        Debug.Log($"<color=yellow>Status Effect Test Results:</color>");
        Debug.Log($"Expected chance: {testAilmentChance:F1}%");
        Debug.Log($"Actual success rate: {actualSuccessRate:F1}% ({successfulApplications}/{totalTests})");
        Debug.Log($"Difference: {Mathf.Abs(actualSuccessRate - testAilmentChance):F1}%");
        
        // For 100% chance, we should have 100% success rate
        if (testAilmentChance == 100f)
        {
            if (actualSuccessRate == 100f)
            {
                Debug.Log("<color=green>✓ 100% chance test PASSED - All effects applied correctly!</color>");
            }
            else
            {
                Debug.LogError($"<color=red>✗ 100% chance test FAILED - Expected 100% but got {actualSuccessRate:F1}%</color>");
            }
        }
        else
        {
            // For other percentages, allow some variance (±10% is reasonable for random tests)
            float variance = Mathf.Abs(actualSuccessRate - testAilmentChance);
            if (variance <= 10f)
            {
                Debug.Log($"<color=green>✓ {testAilmentChance:F0}% chance test PASSED - Within acceptable variance</color>");
            }
            else
            {
                Debug.LogWarning($"<color=orange>⚠ {testAilmentChance:F0}% chance test - High variance: {variance:F1}%</color>");
            }
        }
    }
    
    private int CountActiveEffects()
    {
        if (testStatusEffectManager == null) return 0;
        
        // Use reflection to access private activeEffects field
        var field = typeof(StatusEffectManager).GetField("activeEffects", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (field != null && field.GetValue(testStatusEffectManager) is System.Collections.IList list)
        {
            return list.Count;
        }
        
        return 0;
    }
    
    [Button("Clear All Status Effects")]
    public void ClearStatusEffects()
    {
        if (testStatusEffectManager != null)
        {
            // Destroy and recreate the status effect manager to clear all effects
            DestroyImmediate(testStatusEffectManager);
            testStatusEffectManager = gameObject.AddComponent<StatusEffectManager>();
            
            Debug.Log("All status effects cleared");
        }
    }
}
