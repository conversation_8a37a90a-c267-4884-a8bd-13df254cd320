using UnityEngine;
using Sirenix.OdinInspector;

/// <summary>
/// Test script to verify status effect chance logic works correctly
/// </summary>
public class StatusEffectChanceTest : MonoBehaviour
{
    [Title("Test Configuration")]
    [Range(0f, 100f)]
    [SerializeField] private float testAilmentChance = 100f;
    
    [SerializeField] private DamageType testDamageType = DamageType.Fire;
    
    [Title("Test Results")]
    [ShowInInspector, ReadOnly]
    private int totalTests = 0;
    
    [ShowInInspector, ReadOnly]
    private int successfulApplications = 0;
    
    [ShowInInspector, ReadOnly]
    private float actualSuccessRate = 0f;
    
    [Header("Debug Settings")]
    [SerializeField] private bool enableDebugLogging = false;
    
    private StatusEffectManager testStatusEffectManager;
    
    private void Awake()
    {
        // Create a test status effect manager
        testStatusEffectManager = gameObject.AddComponent<StatusEffectManager>();
    }
    
    [Button("Test Status Effect Chance (100 iterations)")]
    public void TestStatusEffectChance()
    {
        TestStatusEffectChance(100);
    }
    
    [Button("Test Status Effect Chance (1000 iterations)")]
    public void TestStatusEffectChanceLarge()
    {
        TestStatusEffectChance(1000);
    }
    
    private void TestStatusEffectChance(int iterations)
    {
        totalTests = 0;
        successfulApplications = 0;
        
        for (int i = 0; i < iterations; i++)
        {
            totalTests++;
            
            // Create test damage info
            DamageInfo damageInfo = new DamageInfo(
                10f, // damage amount
                testDamageType,
                false, // not critical
                1f, // crit multiplier
                "Test", // source
                testAilmentChance // ailment chance
            );
            
            // Count current active effects before test
            int effectsBeforeTest = CountActiveEffects();
            
            // Try to apply ailment
            StatusEffectHelper.TryApplyAilment(damageInfo, 10f, testStatusEffectManager);
            
            // Count effects after test
            int effectsAfterTest = CountActiveEffects();
            
            // If more effects were added, the application was successful
            if (effectsAfterTest > effectsBeforeTest)
            {
                successfulApplications++;
                
                if (enableDebugLogging)
                {
                    Debug.Log($"Test {i + 1}: Status effect applied successfully");
                }
            }
            else if (enableDebugLogging)
            {
                Debug.Log($"Test {i + 1}: Status effect was not applied");
            }
        }
        
        actualSuccessRate = (float)successfulApplications / totalTests * 100f;
        
        Debug.Log($"<color=yellow>Status Effect Test Results:</color>");
        Debug.Log($"Expected chance: {testAilmentChance:F1}%");
        Debug.Log($"Actual success rate: {actualSuccessRate:F1}% ({successfulApplications}/{totalTests})");
        Debug.Log($"Difference: {Mathf.Abs(actualSuccessRate - testAilmentChance):F1}%");
        
        // For 100% chance, we should have 100% success rate
        if (testAilmentChance == 100f)
        {
            if (actualSuccessRate == 100f)
            {
                Debug.Log("<color=green>✓ 100% chance test PASSED - All effects applied correctly!</color>");
            }
            else
            {
                Debug.LogError($"<color=red>✗ 100% chance test FAILED - Expected 100% but got {actualSuccessRate:F1}%</color>");
            }
        }
        else
        {
            // For other percentages, allow some variance (±10% is reasonable for random tests)
            float variance = Mathf.Abs(actualSuccessRate - testAilmentChance);
            if (variance <= 10f)
            {
                Debug.Log($"<color=green>✓ {testAilmentChance:F0}% chance test PASSED - Within acceptable variance</color>");
            }
            else
            {
                Debug.LogWarning($"<color=orange>⚠ {testAilmentChance:F0}% chance test - High variance: {variance:F1}%</color>");
            }
        }
    }
    
    private int CountActiveEffects()
    {
        if (testStatusEffectManager == null) return 0;
        
        // Use reflection to access private activeEffects field
        var field = typeof(StatusEffectManager).GetField("activeEffects", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (field != null && field.GetValue(testStatusEffectManager) is System.Collections.IList list)
        {
            return list.Count;
        }
        
        return 0;
    }
    
    [Button("Clear All Status Effects")]
    public void ClearStatusEffects()
    {
        if (testStatusEffectManager != null)
        {
            // Destroy and recreate the status effect manager to clear all effects
            DestroyImmediate(testStatusEffectManager);
            testStatusEffectManager = gameObject.AddComponent<StatusEffectManager>();

            Debug.Log("All status effects cleared");
        }
    }

    [Button("Test Fork Projectile Status Effects")]
    public void TestForkProjectileStatusEffects()
    {
        Debug.Log("<color=yellow>=== Fork Projectile Status Effect Test ===</color>");

        // Test with different ailment chances
        float[] testChances = { 25f, 50f, 75f, 100f };

        foreach (float chance in testChances)
        {
            Debug.Log($"\n<color=cyan>Testing {chance}% ailment chance:</color>");

            // Test original projectile hit
            TestProjectileHit(chance, "Original", false);

            // Test forked projectile hit
            TestProjectileHit(chance, "Forked", true);
        }
    }

    private void TestProjectileHit(float ailmentChance, string projectileType, bool isForked)
    {
        int successes = 0;
        int trials = 20;

        for (int i = 0; i < trials; i++)
        {
            // Clear effects before each test
            if (testStatusEffectManager != null)
            {
                DestroyImmediate(testStatusEffectManager);
                testStatusEffectManager = gameObject.AddComponent<StatusEffectManager>();
            }

            // Create damage info
            DamageInfo damageInfo = new DamageInfo(
                10f,
                DamageType.Fire,
                false,
                1f,
                isForked ? "Forked_Projectile" : "Original_Projectile",
                ailmentChance
            );

            // Count effects before
            int effectsBefore = CountActiveEffects();

            // Apply ailment
            StatusEffectHelper.TryApplyAilment(damageInfo, 10f, testStatusEffectManager);

            // Count effects after
            int effectsAfter = CountActiveEffects();

            if (effectsAfter > effectsBefore)
            {
                successes++;
            }
        }

        float successRate = (float)successes / trials * 100f;
        string color = Mathf.Abs(successRate - ailmentChance) <= 15f ? "green" : "red";

        Debug.Log($"  <color={color}>{projectileType}: {successRate:F1}% success rate ({successes}/{trials}) - Expected: {ailmentChance}%</color>");
    }

    [Button("Test Random.value Edge Cases")]
    public void TestRandomValueEdgeCases()
    {
        Debug.Log("<color=yellow>=== Random.value Edge Case Test ===</color>");

        // Test the edge case where Random.value returns exactly 1.0
        // This was the original bug - when ailmentChance is 100% and Random.value returns 1.0,
        // the condition Random.value >= 1.0 would be true, causing the effect to not apply

        int testCount = 10000;
        int exactOneCount = 0;

        for (int i = 0; i < testCount; i++)
        {
            float randomValue = Random.value;
            if (randomValue == 1.0f)
            {
                exactOneCount++;
            }
        }

        Debug.Log($"Out of {testCount} Random.value calls, {exactOneCount} returned exactly 1.0");
        Debug.Log($"Probability of Random.value == 1.0: {(float)exactOneCount / testCount * 100f:F4}%");

        // Test the fixed logic with edge cases
        TestEdgeCaseLogic(100f, 1.0f); // 100% chance with max random value
        TestEdgeCaseLogic(100f, 0.999f); // 100% chance with near-max random value
        TestEdgeCaseLogic(50f, 0.5f); // 50% chance with exact threshold
        TestEdgeCaseLogic(0f, 0.0f); // 0% chance with min random value
    }

    private void TestEdgeCaseLogic(float ailmentChance, float mockRandomValue)
    {
        // Simulate the fixed logic: Random.value > ailmentChance / 100f
        float threshold = ailmentChance / 100f;
        bool shouldApply = mockRandomValue <= threshold;
        bool wouldApplyWithFixedLogic = !(mockRandomValue > threshold);

        string result = shouldApply == wouldApplyWithFixedLogic ? "CORRECT" : "ERROR";
        string color = result == "CORRECT" ? "green" : "red";

        Debug.Log($"<color={color}>Chance: {ailmentChance}%, Random: {mockRandomValue:F3}, Threshold: {threshold:F3}, Should Apply: {shouldApply}, Fixed Logic: {wouldApplyWithFixedLogic} - {result}</color>");
    }
}
