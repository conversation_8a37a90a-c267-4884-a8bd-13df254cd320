using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Events;
using Sirenix.OdinInspector;

public class GemManager : MonoBehaviour
{
    [Title("Gem Storage")]
    [ShowInInspector, ReadOnly]
    private List<GemInstance> inventoryGems = new List<GemInstance>();

    [ShowInInspector, ReadOnly]
    private Dictionary<int, GemInstance> equippedSkillGems = new Dictionary<int, GemInstance>(); // Key: Slot Index

    [ShowInInspector, ReadOnly]
    private Dictionary<int, List<GemInstance>> equippedSupportGems = new Dictionary<int, List<GemInstance>>(); // Key: Skill Slot Index

    [Title("Events")]
    public UnityEvent OnInventoryChanged;

    [Header("Debug Settings")]
    [SerializeField] private bool enableDebugLogging = false;
    [Tooltip("Enable debug logging for gem equipment operations")]

    #region Public Accessors
    // Performance optimization: Return read-only views instead of creating copies
    // This prevents allocation and GC pressure in Unity
    // Cached empty read-only list to avoid allocations when no items exist
    private static readonly IReadOnlyList<GemInstance> EmptyGemList = new List<GemInstance>().AsReadOnly();
    
    public IReadOnlyList<GemInstance> GetAllInventoryGems() => inventoryGems;
    public GemInstance GetEquippedSkillGem(int slotIndex) => equippedSkillGems.GetValueOrDefault(slotIndex);
    public IReadOnlyList<GemInstance> GetEquippedSupportGems(int skillSlotIndex) 
    {
        return equippedSupportGems.TryGetValue(skillSlotIndex, out var supportList) ? supportList : EmptyGemList;
    }
    
    // Return read-only view of equipped skill gems dictionary for inspection
    public IReadOnlyDictionary<int, GemInstance> GetAllEquippedGems() => equippedSkillGems;
    
    public int GetEquippedSlotForGem(GemInstance instance)
    {
        if (instance == null) return -1;
    
        foreach (var kvp in equippedSkillGems)
        {
            if (kvp.Value == instance)
            {
                return kvp.Key;
            }
        }
    
        return -1;
    }

    // Legacy support - returns GemData templates (marked obsolete)
    [Obsolete("Use GetAllInventoryGems() and access gemDataTemplate directly")]
    public List<GemData> GetAllInventoryGemsData() => inventoryGems.Select(i => i.gemDataTemplate).ToList();
    
    [Obsolete("Use GetEquippedSkillGem() and access gemDataTemplate directly")]
    public GemData GetEquippedSkillGemData(int slotIndex) => GetEquippedSkillGem(slotIndex)?.gemDataTemplate;
    #endregion

    #region Tag Compatibility
    
    /// <summary>
    /// Checks if a support gem is compatible with a skill gem based on their tags
    /// </summary>
    private bool IsCompatible(GemInstance skillGem, GemInstance supportGem)
    {
        if (skillGem == null || supportGem == null)
            return false;
            
        var skillData = skillGem.gemDataTemplate as SkillGemData;
        var supportData = supportGem.gemDataTemplate as SupportGemData;
        
        if (skillData == null || supportData == null)
            return false;
            
        // Check if there's any tag overlap between skill gem tags and support gem compatible tags
        return (skillData.gemTags & supportData.compatibleTags) != GemTag.None;
    }
    
    /// <summary>
    /// Public method to check compatibility (for UI feedback)
    /// </summary>
    public bool CanSupportGemSupport(GemInstance supportGem, GemInstance skillGem)
    {
        return IsCompatible(skillGem, supportGem);
    }
    
    #endregion

    #region Gem Manipulation
    
    // Create new instance from template (for drops/rewards)
    public GemInstance AddGemToInventory(GemData gemTemplate, bool generateRandomModifiers = true, GemRarity? rarity = null)
    {
        if (gemTemplate == null) return null;
        
        // Use provided rarity or default to Common
        GemRarity selectedRarity = rarity ?? GemRarity.Common;
        
        GemInstance instance;
        if (generateRandomModifiers && gemTemplate is SupportGemData && SupportGemModifierGenerator.Instance != null)
        {
            // Support gems get random modifiers
            instance = SupportGemModifierGenerator.Instance.CreateGemInstance(gemTemplate, selectedRarity);
        }
        else
        {
            // Skill gems or no random modifiers
            instance = new GemInstance(gemTemplate, selectedRarity);
        }
        
        inventoryGems.Add(instance);

        if (enableDebugLogging)
        {
            Debug.Log($"[GemManager] Added new {instance.DisplayName} to inventory. Total gems: {inventoryGems.Count}");
            if (instance.randomModifiers.Count > 0)
            {
                Debug.Log($"[GemManager] Gem has {instance.randomModifiers.Count} random modifiers");
            }
            Debug.Log($"[GemManager] Invoking OnInventoryChanged event...");
        }
        OnInventoryChanged?.Invoke();
        if (enableDebugLogging)
        {
            Debug.Log($"[GemManager] OnInventoryChanged event invoked.");
        }
        
        return instance;
    }
    
    // Add existing instance (for unequip)
    public void AddGemInstanceToInventory(GemInstance instance)
    {
        if (instance == null || inventoryGems.Contains(instance)) return;
        
        inventoryGems.Add(instance);
        OnInventoryChanged?.Invoke();
        UnityEngine.Debug.Log($"[GemManager] Added {instance.DisplayName} to inventory.");
    }

    public void RemoveGemFromInventory(GemInstance instance)
    {
        if (instance == null) return;
        if (inventoryGems.Remove(instance))
        {
            OnInventoryChanged?.Invoke();
            UnityEngine.Debug.Log($"[GemManager] Removed {instance.DisplayName} from inventory.");
        }
    }

    public void EquipSkillGem(int slotIndex, GemInstance instance)
    {
        if (instance?.gemDataTemplate == null || !instance.IsSkillGem)
        {
            UnityEngine.Debug.LogError($"[GemManager] Invalid skill gem instance for slot {slotIndex}.");
            return;
        }

        // If a gem is already in the slot, unequip it first
        if (equippedSkillGems.ContainsKey(slotIndex))
        {
            UnequipSkillGem(slotIndex);
        }

        // Remove from inventory and equip
        inventoryGems.Remove(instance);
        equippedSkillGems[slotIndex] = instance;
        equippedSupportGems[slotIndex] = new List<GemInstance>(); // Initialize support gem list

        // Invalidate SkillExecutor cache for this slot to ensure fresh data
        var skillExecutor = FindFirstObjectByType<SkillExecutor>();
        if (skillExecutor != null)
        {
            skillExecutor.InvalidateCache(slotIndex);
        }

        OnInventoryChanged?.Invoke();
        if (enableDebugLogging)
        {
            Debug.Log($"[GemManager] Equipped {instance.DisplayName} to skill slot {slotIndex}.");
        }
    }
    
    public void UnequipSkillGem(int slotIndex)
    {
        if (!equippedSkillGems.TryGetValue(slotIndex, out var instance)) return;

        // Unequip all attached support gems first
        UnequipAllSupportGems(slotIndex);

        // Move skill gem back to inventory
        equippedSkillGems.Remove(slotIndex);
        AddGemInstanceToInventory(instance); // This already invokes OnInventoryChanged

        // Invalidate SkillExecutor cache for this slot
        var skillExecutor = FindFirstObjectByType<SkillExecutor>();
        if (skillExecutor != null)
        {
            skillExecutor.InvalidateCache(slotIndex);
        }

        UnityEngine.Debug.Log($"[GemManager] Unequipped {instance.DisplayName} from skill slot {slotIndex}.");
    }

    public bool EquipSupportGem(int skillSlotIndex, GemInstance supportInstance)
    {
        if (supportInstance?.gemDataTemplate == null || !supportInstance.IsSupportGem)
        {
            UnityEngine.Debug.LogError($"[GemManager] Invalid support gem instance.");
            return false;
        }

        if (!equippedSkillGems.ContainsKey(skillSlotIndex))
        {
            UnityEngine.Debug.LogError($"[GemManager] Cannot equip support gem. No skill gem in slot {skillSlotIndex}.");
            return false;
        }
        
        // Check tag compatibility
        var skillGem = equippedSkillGems[skillSlotIndex];
        if (!IsCompatible(skillGem, supportInstance))
        {
            var skillData = skillGem.gemDataTemplate as SkillGemData;
            var supportData = supportInstance.gemDataTemplate as SupportGemData;
            
            UnityEngine.Debug.LogWarning($"[GemManager] Support gem '{supportInstance.DisplayName}' is not compatible with skill gem '{skillGem.DisplayName}'. " +
                $"Skill tags: {skillData?.gemTags}, Support compatible tags: {supportData?.compatibleTags}");
            return false;
        }

        if (!equippedSupportGems.TryGetValue(skillSlotIndex, out var supportList))
        {
            supportList = new List<GemInstance>();
            equippedSupportGems[skillSlotIndex] = supportList;
        }
        
        // Check slot limit
        if (supportList.Count >= skillGem.GetSupportSlotCount())
        {
            UnityEngine.Debug.LogWarning($"[GemManager] Support slot limit reached for skill in slot {skillSlotIndex}.");
            return false;
        }
        
        inventoryGems.Remove(supportInstance);
        supportList.Add(supportInstance);

        // Invalidate SkillExecutor cache for this slot since support gems affect skill calculations
        var skillExecutor = FindFirstObjectByType<SkillExecutor>();
        if (skillExecutor != null)
        {
            skillExecutor.InvalidateCache(skillSlotIndex);
        }

        OnInventoryChanged?.Invoke();
        if (enableDebugLogging)
        {
            Debug.Log($"[GemManager] Equipped {supportInstance.DisplayName} to skill in slot {skillSlotIndex}.");
        }
        return true;
    }

    public void UnequipSupportGem(int skillSlotIndex, GemInstance supportInstance)
    {
        if (!equippedSupportGems.TryGetValue(skillSlotIndex, out var supportList)) return;
        
        if(supportList.Remove(supportInstance))
        {
            AddGemInstanceToInventory(supportInstance); // This already invokes OnInventoryChanged
            
            // Invalidate SkillExecutor cache for this slot since support gems affect skill calculations
            var skillExecutor = FindFirstObjectByType<SkillExecutor>();
            if (skillExecutor != null)
            {
                skillExecutor.InvalidateCache(skillSlotIndex);
            }
            
            UnityEngine.Debug.Log($"[GemManager] Unequipped {supportInstance.DisplayName} from skill in slot {skillSlotIndex}.");
        }
    }
    
    /// <summary>
    /// Removes a support gem from equipment without adding it to inventory (for manual positioning)
    /// </summary>
    public bool RemoveSupportGemFromEquipment(int skillSlotIndex, GemInstance supportInstance)
    {
        if (!equippedSupportGems.TryGetValue(skillSlotIndex, out var supportList)) return false;
        
        if(supportList.Remove(supportInstance))
        {
            // Invalidate SkillExecutor cache for this slot since support gems affect skill calculations
            var skillExecutor = FindFirstObjectByType<SkillExecutor>();
            if (skillExecutor != null)
            {
                skillExecutor.InvalidateCache(skillSlotIndex);
            }
            
            UnityEngine.Debug.Log($"[GemManager] Removed {supportInstance.DisplayName} from skill in slot {skillSlotIndex} (manual positioning).");
            return true;
        }
        
        return false;
    }
    
    private void UnequipAllSupportGems(int skillSlotIndex)
    {
        if (!equippedSupportGems.TryGetValue(skillSlotIndex, out var supportList)) return;

        // Create a copy to iterate over while modifying the original list
        var gemsToUnequip = new List<GemInstance>(supportList);
        foreach (var supportGem in gemsToUnequip)
        {
            UnequipSupportGem(skillSlotIndex, supportGem);
        }
    }

    public void SwapSupportGems(int skillSlotIndex, int indexA, int indexB)
    {
        if (!equippedSupportGems.TryGetValue(skillSlotIndex, out var supportList))
            return;
            
        if (indexA < 0 || indexA >= supportList.Count || indexB < 0 || indexB >= supportList.Count)
            return;
            
        // Swap the gems
        var temp = supportList[indexA];
        supportList[indexA] = supportList[indexB];
        supportList[indexB] = temp;
        
        OnInventoryChanged?.Invoke();
        UnityEngine.Debug.Log($"[GemManager] Swapped support gems at indices {indexA} and {indexB} in skill slot {skillSlotIndex}.");
    }
    
    public void SwapEquippedSkillGems(int indexA, int indexB)
    {
        if (indexA == indexB) return;

        var gemA = GetEquippedSkillGem(indexA);
        var gemB = GetEquippedSkillGem(indexB);

        // Get support gems for both slots
        var supportGemsA = equippedSupportGems.ContainsKey(indexA) ? new List<GemInstance>(equippedSupportGems[indexA]) : new List<GemInstance>();
        var supportGemsB = equippedSupportGems.ContainsKey(indexB) ? new List<GemInstance>(equippedSupportGems[indexB]) : new List<GemInstance>();

        // Clear support gems from both slots
        if (equippedSupportGems.ContainsKey(indexA)) equippedSupportGems[indexA].Clear();
        if (equippedSupportGems.ContainsKey(indexB)) equippedSupportGems[indexB].Clear();

        // Unequip both skill gems
        if (gemA != null) equippedSkillGems.Remove(indexA);
        if (gemB != null) equippedSkillGems.Remove(indexB);
        
        // Equip skill gems in swapped positions
        if (gemA != null) equippedSkillGems[indexB] = gemA;
        if (gemB != null) equippedSkillGems[indexA] = gemB;

        // Move support gems to follow their skill gems
        if (gemA != null && supportGemsA.Count > 0)
        {
            equippedSupportGems[indexB] = supportGemsA;
        }
        if (gemB != null && supportGemsB.Count > 0)
        {
            equippedSupportGems[indexA] = supportGemsB;
        }

        OnInventoryChanged?.Invoke();
        UnityEngine.Debug.Log($"[GemManager] Swapped gems in slots {indexA} and {indexB} with their support gems.");
    }
    #endregion
    
    #region Debug
    [Button("Debug: Add Random Skill Gem")]
    private void DebugAddRandomSkillGem()
    {
        var allSkillGems = Resources.FindObjectsOfTypeAll<SkillGemData>();
        if (allSkillGems.Length > 0)
        {
            var randomGem = allSkillGems[UnityEngine.Random.Range(0, allSkillGems.Length)];
            AddGemToInventory(randomGem);
        }
    }

    [Button("Debug: Add Random Support Gem")]
    private void DebugAddRandomSupportGem()
    {
        var allSupportGems = Resources.FindObjectsOfTypeAll<SupportGemData>();
        if (allSupportGems.Length > 0)
        {
            var randomGem = allSupportGems[UnityEngine.Random.Range(0, allSupportGems.Length)];
            AddGemToInventory(randomGem);
        }
    }
    
    [Button("Debug: Add Multiple Fireballs")]
    private void DebugAddMultipleFireballs()
    {
        var fireballs = Resources.FindObjectsOfTypeAll<SkillGemData>()
            .Where(g => g.gemName.Contains("Fireball"))
            .FirstOrDefault();
            
        if (fireballs != null)
        {
            for (int i = 0; i < 3; i++)
            {
                AddGemToInventory(fireballs);
            }
        }
    }
    #endregion
}