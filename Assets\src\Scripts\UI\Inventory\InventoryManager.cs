using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;
using Sirenix.OdinInspector;
using TMPro;

public class InventoryManager : MonoBehaviour
{
    [Title("References")]
    [Required]
    public GameObject inventoryPanel;
    
    [Required]
    [InfoBox("Container with manually created InventorySlot children. The number of child slots determines inventory size.")]
    public Transform inventorySlotContainer;        
    [Required]
    public EquipmentPanel equipmentPanel;
    
    [Required]
    [InfoBox("The GameObject containing the Equipment Panel - will be toggled with inventory")]
    public GameObject equipmentPanelObject;
    
    [Title("Tooltip")]
    [Required]
    public GameObject tooltipObject;
    
    [Required]
    public TextMeshProUGUI tooltipText;
    
    [Title("Drag & Drop")]
    [InfoBox("Optional: A separate icon that follows the mouse while dragging")]
    public GameObject dragIconPrefab;
    
    private GameObject currentDragIcon;
    
    [Title("Settings")]
    [ReadOnly]
    [InfoBox("Inventory size is automatically determined by the number of InventorySlot children in the container.")]
    public int inventorySize = 40;        
    [Title("Debug")]
    [ShowInInspector]
    [ReadOnly]
    private List<InventorySlot> inventorySlots = new List<InventorySlot>();

    [ShowInInspector]
    [ReadOnly]
    private GemInstance[] inventory;

    [Header("Debug Settings")]
    [SerializeField] private bool enableDebugLogging = false;
    [Tooltip("Enable debug logging for inventory state changes")]
    
    // Public accessors for bridge
    public List<InventorySlot> InventorySlots => inventorySlots;
    public List<GemInstance> Inventory => inventory.Where(g => g != null).ToList();
    
    // Legacy support
    public List<GemData> GetInventoryGemData() => inventory.Where(i => i != null).Select(i => i.gemDataTemplate).ToList();
    
    private bool isOpen = false;
    
    private void Awake()
    {
        InitializeInventory();
        inventory = new GemInstance[inventorySize];
        HideTooltip();
        
        inventoryPanel.gameObject.SetActive(false);
        if (equipmentPanelObject != null)
            equipmentPanelObject.SetActive(false);
    }        
    private void Start()
    {
        // The InventoryGemBridge is now responsible for all connections and syncing.
        ValidateSetup();
    }
    
    private void ValidateSetup()
    {
        if (inventorySlotContainer == null)
        {
            Debug.LogError("[InventoryManager] inventorySlotContainer is not assigned! Please assign it in the inspector.");
            return;
        }
        
        if (inventorySlots.Count == 0)
        {
            Debug.LogError("[InventoryManager] No inventory slots found! Use the 'Inventory Slot Creator' tool (2D Rogue → Inventory System → Inventory Slot Creator) to create slots manually.");
            return;
        }
        
        // Check if all slots have required components
        int invalidSlots = 0;
        for (int i = 0; i < inventorySlots.Count; i++)
        {
            var slot = inventorySlots[i];
            if (slot.layeredIconDisplay == null)
            {
                Debug.LogError($"[InventoryManager] Slot {i} ({slot.name}) is missing LayeredIconDisplay component!");
                invalidSlots++;
            }
            if (slot.borderImage == null)
            {
                Debug.LogWarning($"[InventoryManager] Slot {i} ({slot.name}) is missing borderImage reference.");
            }
        }
        
        if (invalidSlots > 0)
        {
            Debug.LogError($"[InventoryManager] {invalidSlots} slots have missing components. Use the LayeredIconSetup tool to fix them.");
        }
        else
        {
            Debug.Log($"[InventoryManager] Successfully validated {inventorySlots.Count} inventory slots.");
        }
    }        
    private void OnDestroy()
    {
    }
    
    private void InitializeInventory()
    {
        // Find all manually created InventorySlot children
        inventorySlots.Clear();
        
        if (inventorySlotContainer == null)
        {
            Debug.LogError("[InventoryManager] inventorySlotContainer is null! Please assign it in the inspector.");
            return;
        }
        
        // Get all InventorySlot components from direct children
        var slots = inventorySlotContainer.GetComponentsInChildren<InventorySlot>();
        
        if (slots.Length == 0)
        {
            Debug.LogError("[InventoryManager] No InventorySlot components found in inventorySlotContainer! Please add InventorySlot children manually.");
            return;
        }
        
        // Initialize each slot and add to list
        foreach (var slot in slots)
        {
            slot.Initialize(this);
            inventorySlots.Add(slot);
        }
        
        // Update inventory size based on actual slot count
        inventorySize = inventorySlots.Count;
        
        Debug.Log($"[InventoryManager] Initialized {inventorySize} inventory slots from manually created children.");
    }        
    public void ToggleInventory()
    {
        if (enableDebugLogging)
        {
            Debug.Log($"[InventoryManager] ToggleInventory called. Current isOpen={isOpen}");
        }

        isOpen = !isOpen;

        // Set both panels to the same state
        if (inventoryPanel != null)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[InventoryManager] Setting inventoryPanel to {isOpen}");
            }
            inventoryPanel.gameObject.SetActive(isOpen);
        }
        else
        {
            Debug.LogError("[InventoryManager] inventoryPanel is null! Check Inspector references!");
        }
        
        if (equipmentPanelObject != null)
        {
            Debug.Log($"[InventoryManager] Setting equipmentPanelObject to {isOpen}");
            equipmentPanelObject.SetActive(isOpen);
        }
        else
        {
            Debug.LogWarning("[InventoryManager] equipmentPanelObject is null!");
        }
        
        if (isOpen)
        {
            // Don't sync on every open - only refresh display
            RefreshDisplay();
            // Force immediate canvas update to prevent initialization delays
            Canvas.ForceUpdateCanvases();
        }
        else
        {
            HideTooltip();
        }
    }
    
    public bool AddGem(GemInstance instance)
    {
        if (instance == null)
            return false;
        
        // Find first empty slot
        for (int i = 0; i < inventory.Length; i++)
        {
            if (inventory[i] == null)
            {
                inventory[i] = instance;
                RefreshDisplay();
                return true;
            }
        }
        
        return false; // Inventory full
    }
    
    public bool AddGemWithoutRefresh(GemInstance instance)
    {
        if (instance == null)
            return false;
        
        // Find first empty slot
        for (int i = 0; i < inventory.Length; i++)
        {
            if (inventory[i] == null)
            {
                inventory[i] = instance;
                return true;
            }
        }
        
        return false; // Inventory full
    }
    
    // Legacy support - creates new instance
    public bool AddGem(GemData gemData)
    {
        if (gemData == null) return false;
        var instance = new GemInstance(gemData);
        return AddGem(instance);
    }
    
    // Legacy support - creates new instance
    public bool AddGemWithoutRefresh(GemData gemData)
    {
        if (gemData == null) return false;
        var instance = new GemInstance(gemData);
        return AddGemWithoutRefresh(instance);
    }
    
    public void ForceRefreshDisplay()
    {
        RefreshDisplay();
    }
    
    private void RefreshDisplay()
    {
        for (int i = 0; i < inventorySlots.Count && i < inventory.Length; i++)
        {
            inventorySlots[i].SetGemInstance(inventory[i]);
        }
    }
    
    public void SwapGems(InventorySlot sourceSlot, InventorySlot targetSlot)
    {
        if (sourceSlot == null || targetSlot == null || sourceSlot == targetSlot)
            return;
            
        int sourceIndex = inventorySlots.IndexOf(sourceSlot);
        int targetIndex = inventorySlots.IndexOf(targetSlot);
        
        if (sourceIndex < 0 || targetIndex < 0 || sourceIndex >= inventory.Length || targetIndex >= inventory.Length)
            return;
            
        // Simple swap - works for all cases (empty or full slots)
        var temp = inventory[sourceIndex];
        inventory[sourceIndex] = inventory[targetIndex];
        inventory[targetIndex] = temp;
        
        RefreshDisplay();
    }
    
    public bool RemoveGem(GemInstance instance)
    {
        if (instance == null)
            return false;
            
        // Find and remove the instance
        for (int i = 0; i < inventory.Length; i++)
        {
            if (inventory[i] == instance)
            {
                inventory[i] = null;
                RefreshDisplay();
                return true;
            }
        }
        
        return false;
    }
    
    // Legacy support - removes first gem with matching template
    public bool RemoveGem(GemData gemData)
    {
        if (gemData == null)
            return false;
            
        // Find and remove the first matching gem template
        for (int i = 0; i < inventory.Length; i++)
        {
            if (inventory[i]?.gemDataTemplate == gemData)
            {
                inventory[i] = null;
                RefreshDisplay();
                return true;
            }
        }
        
        return false;
    }
    
    public void RemoveGemAt(int index)
    {
        if (index >= 0 && index < inventory.Length)
        {
            inventory[index] = null;
            RefreshDisplay();
        }
    }
    
    public void ClearInventory()
    {
        for (int i = 0; i < inventory.Length; i++)
        {
            inventory[i] = null;
        }
        RefreshDisplay();
    }
    
    public void ReturnGemToInventory(GemInstance instance)
    {
        if (instance != null)
        {
            AddGem(instance);
        }
    }
    
    public void AddGemToSlot(GemInstance instance, InventorySlot targetSlot)
    {
        if (instance == null || targetSlot == null)
        {
            return;
        }
    
        int targetIndex = inventorySlots.IndexOf(targetSlot);
    
        if (targetIndex < 0 || targetIndex >= inventory.Length)
        {
            // Invalid slot, add to first available as fallback
            AddGem(instance);
            return;
        }
    
        // Only place in empty slots. DragDropManager should handle occupied slots.
        if (inventory[targetIndex] == null)
        {
            inventory[targetIndex] = instance;
            RefreshDisplay();
        }
        else
        {
            // Fallback to adding to the first available slot if the target is occupied.
            // This might still cause a "jump" but it's better than losing the gem.
            AddGem(instance);
        }
    }
    
    // Legacy support
    public void ReturnGemToInventory(GemData gem)
    {
        if (gem != null)
        {
            AddGem(gem);
        }
    }
    
    /// <summary>
    /// After the GemManager has updated the master list, this method rearranges the local
    /// inventory array to move a gem to a specific slot, fixing its visual position.
    /// </summary>
    public void MoveGemToSlot(GemInstance gemToMove, InventorySlot newSlot)
    {
        if (gemToMove == null || newSlot == null) return;
    
        int newIndex = inventorySlots.IndexOf(newSlot);
        if (newIndex < 0 || newIndex >= inventory.Length) return;
    
        int oldIndex = -1;
        for (int i = 0; i < inventory.Length; i++)
        {
            if (inventory[i] == gemToMove)
            {
                oldIndex = i;
                break;
            }
        }
    
        if (oldIndex != -1 && oldIndex != newIndex)
        {
            // Swap the gem at the new index with the one at the old index.
            // This handles cases where the new slot is empty (swapping with null) or occupied.
            var otherGem = inventory[newIndex];
            inventory[newIndex] = inventory[oldIndex];
            inventory[oldIndex] = otherGem;
    
            RefreshDisplay();
        }
    }
    
    public void ShowTooltip(GemInstance instance, Vector3 position)
    {
        if (instance == null || tooltipObject == null)
            return;
        
        tooltipObject.SetActive(true);
        tooltipText.text = instance.GetTooltipText();
        
        var tooltipRect = tooltipObject.GetComponent<RectTransform>();
        tooltipRect.position = position + new Vector3(100, 0, 0);
    }
    
    // Overload for custom tooltip text
    public void ShowTooltip(string customTooltipText, Vector3 position)
    {
        if (string.IsNullOrEmpty(customTooltipText) || tooltipObject == null)
            return;
        
        tooltipObject.SetActive(true);
        tooltipText.text = customTooltipText;
        
        var tooltipRect = tooltipObject.GetComponent<RectTransform>();
        tooltipRect.position = position + new Vector3(100, 0, 0);
    }
    
    // Legacy support
    public void ShowTooltip(GemData gem, Vector3 position)
    {
        if (gem == null || tooltipObject == null)
            return;
        
        tooltipObject.SetActive(true);
        tooltipText.text = gem.GetTooltipText();
        
        var tooltipRect = tooltipObject.GetComponent<RectTransform>();
        tooltipRect.position = position + new Vector3(100, 0, 0);
    }
    
    public void HideTooltip()
    {
        if (tooltipObject != null)
            tooltipObject.SetActive(false);
    }
    
    
    [Button("Refresh Inventory Slots")]
    private void RefreshInventorySlots()
    {
        InitializeInventory();
        // Resize inventory array if needed
        if (inventory.Length != inventorySize)
        {
            var newInventory = new GemInstance[inventorySize];
            for (int i = 0; i < Mathf.Min(inventory.Length, inventorySize); i++)
            {
                newInventory[i] = inventory[i];
            }
            inventory = newInventory;
        }
        RefreshDisplay();
        Debug.Log($"Refreshed inventory slots. Found {inventorySize} slots.");
    }
    
    [Button("Add Random Skill Gem (Debug)")]
    private void DebugAddSkillGem()        {
        var allSkillGems = Resources.FindObjectsOfTypeAll<SkillGemData>();
        if (allSkillGems.Length > 0)
        {
            var randomGem = allSkillGems[Random.Range(0, allSkillGems.Length)];
            var instance = new GemInstance(randomGem);
            AddGem(instance);
        }
    }
    
    [Button("Add Random Support Gem (Debug)")]
    private void DebugAddSupportGem()
    {
        var allSupportGems = Resources.FindObjectsOfTypeAll<SupportGemData>();
        if (allSupportGems.Length > 0)
        {
            var randomGem = allSupportGems[Random.Range(0, allSupportGems.Length)];
            var instance = new GemInstance(randomGem);
            AddGem(instance);
        }
    }
    
    [Button("Force Sync with GemManager")]
    private void DebugForceSync()
    {
        var bridge = FindFirstObjectByType<InventoryGemBridge>();
        if(bridge != null)
        {
            // The bridge now has a public method for this
            // bridge.ForceSyncGems(); 
            // Let's assume the button on the bridge is sufficient for now.
            UnityEngine.Debug.Log("Please use the 'Force Refresh All UI' button on the InventoryGemBridge component.");
        }
        else
        {
            UnityEngine.Debug.LogError("Could not find InventoryGemBridge to sync!");
        }
    }
    
    [Button("Debug Connection Status")]
    private void DebugConnectionStatus()
    {
        var gemManager = FindFirstObjectByType<GemManager>();

        UnityEngine.Debug.Log($"=== InventoryManager Connection Status ===");
        UnityEngine.Debug.Log($"GemManager Found: {(gemManager != null ? "Yes" : "NO")}");
        
        if (gemManager != null)
        {
            UnityEngine.Debug.Log($"GemManager inventory has {gemManager.GetAllInventoryGems().Count} gems");
            UnityEngine.Debug.Log($"InventoryManager UI shows {inventory.Count(g => g != null)} gems");
            
            // List all gems
            UnityEngine.Debug.Log("GemManager inventory contents:");
            foreach (var gem in gemManager.GetAllInventoryGems())
            {
                UnityEngine.Debug.Log($"  - {gem.DisplayName}");
            }
            
            UnityEngine.Debug.Log("InventoryManager UI contents:");
            foreach (var item in inventory)
            {
                UnityEngine.Debug.Log($"  - {item.DisplayName}");
            }
        }
        else
        {
            UnityEngine.Debug.Log("Cannot check gem counts - no GemManager found");
        }
    }
}