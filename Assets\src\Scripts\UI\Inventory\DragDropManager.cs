using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

public class DragDropManager : MonoBehaviour
{
    private static DragDropManager instance;
    
    [Header("Drag Visual")]
    public GameObject dragIconPrefab;

    [Header("Debug Settings")]
    [SerializeField] private bool enableDebugLogging = false;
    [Tooltip("Enable debug logging for drag-drop operations")]

    private GameObject currentDragIcon;
    private ISlot sourceSlot;
    private Canvas canvas;
    private List<ISlot> dimmedSlots = new List<ISlot>();
    private Dictionary<ISlot, float> originalAlphaValues = new Dictionary<ISlot, float>();
    
    public static DragDropManager Instance
    {
        get
        {
            if (instance == null)
            {
                instance = FindFirstObjectByType<DragDropManager>();
            }
            return instance;
        }
    }
    
    private void Awake()
    {
        instance = this;
        canvas = GetComponentInParent<Canvas>();
        
        // If no canvas found in parent, try to find the root canvas
        if (canvas == null)
        {
            canvas = FindFirstObjectByType<Canvas>();
        }
    }
    
    public void StartDrag(ISlot slot, PointerEventData eventData)
    {
        if (slot == null || slot.IsEmpty)
            return;
            
        sourceSlot = slot;
        
        // Create drag icon
        if (dragIconPrefab != null)
        {
            currentDragIcon = Instantiate(dragIconPrefab, canvas.transform);
        }
        else
        {
            currentDragIcon = new GameObject("DragIcon");
            currentDragIcon.transform.SetParent(canvas.transform);
            var image = currentDragIcon.AddComponent<Image>();
            
            // Get sprite for drag icon - prioritize layered display main sprite
            Sprite dragSprite = null;
            
            // Try to get sprite from layered icon display first
            var layeredDisplay = slot.gameObject.GetComponentInChildren<LayeredIconDisplay>();
            if (layeredDisplay != null)
            {
                dragSprite = layeredDisplay.GetMainSprite();
            }
            
            // Fallback to instance/template icon
            if (dragSprite == null)
            {
                var instance = slot.CurrentGemInstance;
                if (instance != null)
                {
                    dragSprite = instance.Icon;
                }
                else if (slot.CurrentGem != null)
                {
                    dragSprite = slot.CurrentGem.GetIcon();
                }
            }
            
            image.sprite = dragSprite;                image.raycastTarget = false;
            
            var rect = currentDragIcon.GetComponent<RectTransform>();
            rect.sizeDelta = new Vector2(64, 64);
            
            // Apply gem rarity color to drag icon
            image.color = new Color(1f, 1f, 1f, 0.8f);
        }
        
        UpdateDragPosition(eventData);
        
        // Make source semi-transparent
        var slotGO = slot.gameObject;
        var canvasGroup = slotGO.GetComponent<CanvasGroup>();
        if (canvasGroup == null)
            canvasGroup = slotGO.AddComponent<CanvasGroup>();
        canvasGroup.alpha = 0.5f;
        
        // Make drag icon not block raycasts
        if (currentDragIcon != null)
        {
            var dragCanvasGroup = currentDragIcon.GetComponent<CanvasGroup>();
            if (dragCanvasGroup == null)
                dragCanvasGroup = currentDragIcon.AddComponent<CanvasGroup>();
            dragCanvasGroup.blocksRaycasts = false;
        }
        
        // Apply visual feedback for support gem compatibility
        ApplyCompatibilityVisualFeedback();
    }
    
    public void UpdateDragPosition(PointerEventData eventData)
    {
        if (currentDragIcon == null)
            return;
            
        RectTransformUtility.ScreenPointToLocalPointInRectangle(
            canvas.transform as RectTransform,
            eventData.position,
            eventData.pressEventCamera,
            out Vector2 localPoint
        );
        
        currentDragIcon.transform.localPosition = localPoint;
    }
    
    public void EndDrag(PointerEventData eventData)
    {
        if (currentDragIcon != null)
            Destroy(currentDragIcon);
            
        if (sourceSlot != null)
        {
            // Reset source transparency
            var canvasGroup = sourceSlot.gameObject.GetComponent<CanvasGroup>();
            if (canvasGroup != null)
                canvasGroup.alpha = 1f;
                
            // Restore compatibility visual feedback
            RestoreCompatibilityVisualFeedback();
                
            // Try to find what we dropped on
            var dropTarget = eventData.pointerCurrentRaycast.gameObject;
            if (dropTarget != null)
            {
                var targetSlot = dropTarget.GetComponentInParent<ISlot>();
                if (enableDebugLogging)
                {
                    Debug.Log("DropTarget: " + dropTarget?.name);
                    Debug.Log("TargetSlot: " + targetSlot);
                }
                if (targetSlot != null && targetSlot != sourceSlot)
                {
                    HandleDrop(sourceSlot, targetSlot);
                }
            }
        }
        
        sourceSlot = null;
    }
    
    private void ApplyCompatibilityVisualFeedback()
    {
        if (sourceSlot == null || sourceSlot.IsEmpty || sourceSlot.CurrentGemInstance == null)
            return;
            
        var draggedGem = sourceSlot.CurrentGemInstance;
        
        // Only apply feedback when dragging a support gem FROM inventory
        if (!draggedGem.IsSupportGem || !(sourceSlot is InventorySlot))
            return;
            
        var gemManager = FindFirstObjectByType<GemManager>();
        if (gemManager == null)
            return;
            
        // Get all equipped skill gems for compatibility checking
        var allEquippedSkillGems = gemManager.GetAllEquippedGems().Values;
        bool hasEquippedSkills = allEquippedSkillGems.Any();
        
        // Find all inventory slots with support gems and dim incompatible ones
        var inventorySlots = FindObjectsByType<InventorySlot>(FindObjectsSortMode.None);
        
        foreach (var slot in inventorySlots)
        {
            if (slot == sourceSlot || slot.IsEmpty || slot.CurrentGemInstance == null)
                continue;
                
            var slotGem = slot.CurrentGemInstance;
            if (!slotGem.IsSupportGem)
                continue;
                
            // Check compatibility with all equipped skill gems
            bool isCompatibleWithAnySkill = !hasEquippedSkills; // If no skills equipped, all support gems are "compatible"
            
            if (hasEquippedSkills)
            {
                foreach (var skillGem in allEquippedSkillGems)
                {
                    if (gemManager.CanSupportGemSupport(slotGem, skillGem))
                    {
                        isCompatibleWithAnySkill = true;
                        break;
                    }
                }
            }
            
            // Dim incompatible support gems
            if (!isCompatibleWithAnySkill)
            {
                // Store original alpha before dimming
                if (slot.canvasGroup != null)
                {
                    originalAlphaValues[slot] = slot.canvasGroup.alpha;
                }
                slot.SetAlpha(0.5f);
                dimmedSlots.Add(slot);
            }
        }
        
        // Dim support slots where the dragged gem cannot be equipped
        var supportSlots = FindObjectsByType<SupportGemSlot>(FindObjectsSortMode.None);
        
        foreach (var supportSlot in supportSlots)
        {
            // Get the parent skill slot for this support slot
            var parentSkillSlot = supportSlot.GetParentSkillSlot();
            if (parentSkillSlot == null)
                continue;
                
            // Get the skill gem in the parent slot
            var skillGem = gemManager.GetEquippedSkillGem(parentSkillSlot.GetSlotIndex());
            if (skillGem == null)
            {
                // No skill gem equipped - skip dimming inactive slots
                continue;
            }
            
            // Check if this support slot is active (available)
            if (supportSlot.canvasGroup != null && supportSlot.canvasGroup.alpha < 1.0f)
            {
                // Slot is inactive (alpha < 1.0) - skip dimming
                continue;
            }
            
            // Check if the dragged support gem is compatible with this skill gem
            bool isCompatible = gemManager.CanSupportGemSupport(draggedGem, skillGem);
            
            if (!isCompatible)
            {
                // Store original alpha before dimming
                if (supportSlot.canvasGroup != null)
                {
                    originalAlphaValues[supportSlot] = supportSlot.canvasGroup.alpha;
                }
                // Dim incompatible support slots
                supportSlot.SetAlpha(0.5f);
                dimmedSlots.Add(supportSlot);
            }
        }
    }
    
    private void RestoreCompatibilityVisualFeedback()
    {
        // Restore original alpha for all dimmed slots
        foreach (var slot in dimmedSlots)
        {
            if (originalAlphaValues.TryGetValue(slot, out float originalAlpha))
            {
                if (slot is InventorySlot inventorySlot)
                {
                    inventorySlot.SetAlpha(originalAlpha);
                }
                else if (slot is SupportGemSlot supportSlot)
                {
                    supportSlot.SetAlpha(originalAlpha);
                }
            }
        }
        
        dimmedSlots.Clear();
        originalAlphaValues.Clear();
    }
    
    private void HandleDrop(ISlot source, ISlot target)
    {
        var gemManager = FindFirstObjectByType<GemManager>();
        if (gemManager == null) 
        {
            UnityEngine.Debug.LogError("DragDropManager could not find GemManager!");
            return;
        }
            
        if (source == null || target == null || source == target || source.IsEmpty) return;

        var sourceInstance = source.CurrentGemInstance;
        var targetInstance = target.CurrentGemInstance;

        // Case 1: From Skill Slot
        if (source is SkillGemSlot sourceSkillSlot)
        {
            var inventoryManager = FindFirstObjectByType<InventoryManager>();
            
            // ...to another Skill Slot (SWAP)
            if (target is SkillGemSlot targetSkillSlot)
            {
                if (enableDebugLogging)
                {
                    Debug.Log($"[DragDrop] Swapping skill gems between slots {sourceSkillSlot.GetSlotIndex()} and {targetSkillSlot.GetSlotIndex()}");
                }
                gemManager.SwapEquippedSkillGems(sourceSkillSlot.GetSlotIndex(), targetSkillSlot.GetSlotIndex());
            }
            // ...to Inventory (UNEQUIP/SWAP)
            else if (target is InventorySlot targetInventorySlot)
            {
                // Unequip the source gem. GemManager adds it to its inventory list.
                gemManager.UnequipSkillGem(sourceSkillSlot.GetSlotIndex());

                // If the target slot in inventory was occupied by another skill gem, equip it.
                if (targetInstance != null && targetInstance.IsSkillGem)
                {
                    gemManager.EquipSkillGem(sourceSkillSlot.GetSlotIndex(), targetInstance);
                }

                // Now, correct the visual position of the unequipped gem in the inventory UI.
                if (inventoryManager != null)
                {
                    inventoryManager.MoveGemToSlot(sourceInstance, targetInventorySlot);
                }
            }
        }
        // Case 2: From Inventory
        else if (source is InventorySlot sourceInventorySlot)
        {
            // ...to Skill Slot (EQUIP/SWAP)
            if (target is SkillGemSlot targetSkillSlot)
            {
                if (sourceInstance != null && sourceInstance.IsSkillGem)
                {
                    var inventoryManager = FindFirstObjectByType<InventoryManager>();
                    
                    // Equip the gem. This also unequips the other gem (targetInstance) if present.
                    gemManager.EquipSkillGem(targetSkillSlot.GetSlotIndex(), sourceInstance);

                    // If a gem was unequipped (targetInstance), it is now in the GemManager's inventory list.
                    // The InventoryManager's UI is now out of sync.
                    // We need to manually update the UI to place the unequipped gem in the source slot.
                    if (inventoryManager != null)
                    {
                        var inventory = FindFirstObjectByType<InventoryManager>();
                        inventory.RemoveGem(sourceInstance); // Remove the source gem from the inventory UI
                        if (targetInstance != null)
                        {
                            inventory.AddGemToSlot(targetInstance, sourceInventorySlot); // Add the target gem to the specific slot
                        }
                    }
                }
            }
            // ...to Support Slot (EQUIP SUPPORT GEM)
            else if (target is SupportGemSlot targetSupportSlot)
            {
                HandleInventoryToSupportDrop(sourceInventorySlot, targetSupportSlot);
            }
            // ...to another Inventory Slot (REORDER/SWAP)
            else if (target is InventorySlot targetInventorySlot)
            {
                var inventoryManager = FindFirstObjectByType<InventoryManager>();
                if (inventoryManager != null)
                {
                    inventoryManager.SwapGems(sourceInventorySlot, targetInventorySlot);
                }
            }
        }
        // Case 3: From Support Slot
        else if (source is SupportGemSlot sourceSupportSlot)
        {
            Debug.Log("HandleDrop: Source is SupportGemSlot, Target: " + target);
            HandleSupportGemDrop(sourceSupportSlot, target);
        }
    }
    
    private void HandleSupportGemDrop(SupportGemSlot sourceSupport, ISlot target)
    {
        if (sourceSupport.IsEmpty) return;
        
        var gemManager = FindFirstObjectByType<GemManager>();
        if (gemManager == null) return;
        
        var sourceInstance = sourceSupport.CurrentGemInstance;
        if (sourceInstance == null) return;
        
        // Get parent skill slot index - use the new method instead of GetComponentInParent
        var parentSkillSlot = sourceSupport.GetParentSkillSlot();
        if (parentSkillSlot == null) return;
        
        int skillSlotIndex = parentSkillSlot.GetSlotIndex();
        
        // To another Support Slot (SWAP)
        if (target is SupportGemSlot targetSupport)
        {
            var targetParentSkillSlot = targetSupport.GetComponentInParent<SkillGemSlot>();
            if (targetParentSkillSlot == null) return;
            
            int targetSkillSlotIndex = targetParentSkillSlot.GetSlotIndex();
            var targetInstance = targetSupport.CurrentGemInstance;
            
            // If same skill slot, just swap visually
            if (skillSlotIndex == targetSkillSlotIndex)
            {
                // Get support gems list from GemManager to find indices
                var supportGems = gemManager.GetEquippedSupportGems(skillSlotIndex);
                int sourceIdx = -1;
                int targetIdx = -1;
                
                // Find indices manually since IReadOnlyList doesn't have IndexOf
                for (int i = 0; i < supportGems.Count; i++)
                {
                    if (supportGems[i] == sourceInstance) sourceIdx = i;
                    if (supportGems[i] == targetInstance) targetIdx = i;
                }
                
                if (sourceIdx >= 0 && targetIdx >= 0)
                {
                    // Use GemManager's swap method to properly reorder the gems
                    gemManager.SwapSupportGems(skillSlotIndex, sourceIdx, targetIdx);
                    
                    // Refresh the parent skill slot to update visuals
                    parentSkillSlot.RefreshSupportSlots();
                }
            }
            else
            {
                // Different skill slots - move the gem
                gemManager.UnequipSupportGem(skillSlotIndex, sourceInstance);
                
                // Try to equip to target slot
                if (targetInstance != null)
                {
                    // Swap: unequip target first
                    gemManager.UnequipSupportGem(targetSkillSlotIndex, targetInstance);
                }
                
                gemManager.EquipSupportGem(targetSkillSlotIndex, sourceInstance);
                
                // If we had a target gem, equip it to source slot
                if (targetInstance != null)
                {
                    gemManager.EquipSupportGem(skillSlotIndex, targetInstance);
                }
                
                // Invalidate caches for both skill slots
                var skillExecutor = FindFirstObjectByType<SkillExecutor>();
                if (skillExecutor != null)
                {
                    skillExecutor.InvalidateCache(skillSlotIndex);
                    skillExecutor.InvalidateCache(targetSkillSlotIndex);
                }
            }
        }
        // To Inventory (UNEQUIP)
        else if (target is InventorySlot targetInventorySlot)
        {
            var inventoryManager = FindFirstObjectByType<InventoryManager>();
            if (inventoryManager == null) return;
            
            // Handle target slot first if it has a gem
            var targetInstance = targetInventorySlot.CurrentGemInstance;
            
            if (targetInstance != null && targetInstance.IsSupportGem)
            {
                // Remove target gem from inventory temporarily
                targetInventorySlot.SetGemInstance(null);
                gemManager.EquipSupportGem(skillSlotIndex, targetInstance);
            }
            
            // Check if source gem is actually equipped
            var equippedSupportGems = gemManager.GetEquippedSupportGems(skillSlotIndex);
            
            if (equippedSupportGems.Contains(sourceInstance))
            {
                // Remove from equipment WITHOUT adding to inventory automatically
                bool removed = gemManager.RemoveSupportGemFromEquipment(skillSlotIndex, sourceInstance);
                
                if (removed)
                {
                    // Add to GemManager's authoritative inventory list
                    gemManager.AddGemInstanceToInventory(sourceInstance);
                    
                    // Now move the gem to the desired UI position
                    inventoryManager.MoveGemToSlot(sourceInstance, targetInventorySlot);
                }
            }
            
            // Invalidate skill executor cache when support gems change
            var skillExecutor = FindFirstObjectByType<SkillExecutor>();
            if (skillExecutor != null)
            {
                skillExecutor.InvalidateCache(skillSlotIndex);
            }
        }
    }
    
    private void HandleInventoryToSupportDrop(InventorySlot sourceInventory, SupportGemSlot targetSupport)
    {
        if (enableDebugLogging)
        {
            Debug.Log("Attempting to drop from Inventory to Support Slot.");
        }

        var sourceInstance = sourceInventory.CurrentGemInstance;
        if (sourceInstance == null || !sourceInstance.IsSupportGem)
        {
            if (enableDebugLogging)
            {
                Debug.LogError("Drop failed: Item is not a valid support gem.");
            }
            return;
        }
        
        var gemManager = FindFirstObjectByType<GemManager>();
        if (gemManager == null) return;
        
        // Get parent skill slot
        var parentSkillSlot = targetSupport.GetParentSkillSlot();
        if (parentSkillSlot == null)
        {
            Debug.LogError("Drop failed: Support slot has no assigned parent SkillGemSlot.");
            return;
        }
        
        if (enableDebugLogging)
        {
            Debug.Log("All checks passed, proceeding with drop logic.");
        }
        
        int skillSlotIndex = parentSkillSlot.GetSlotIndex();
        var targetInstance = targetSupport.CurrentGemInstance;
        
        // Try to equip the support gem
        bool equipSuccess = gemManager.EquipSupportGem(skillSlotIndex, sourceInstance);
        
        if (equipSuccess)
        {
            // Only remove from inventory UI if equipping was successful
            var inventoryManager = FindFirstObjectByType<InventoryManager>();
            if (inventoryManager != null)
            {
                inventoryManager.RemoveGem(sourceInstance);
            }
        }
        else
        {
            // If equipping failed, gem stays in inventory - no UI changes needed
            Debug.LogWarning("Support gem equipping failed - gem remains in inventory");
            return;
        }
        
        // If the target had a gem, it goes back to inventory (swap case)
        if (targetInstance != null)
        {
            var inventoryMgr = FindFirstObjectByType<InventoryManager>();
            if (inventoryMgr != null)
            {
                inventoryMgr.AddGemToSlot(targetInstance, sourceInventory);
            }
        }
        
        // Immediately refresh the parent skill slot so the new support gem is visible without waiting for the global UI refresh.
        parentSkillSlot.RefreshSupportSlots();
        
        // Invalidate skill executor cache so support gem effects are applied
        var skillExecutor = FindFirstObjectByType<SkillExecutor>();
        if (skillExecutor != null)
        {
            skillExecutor.InvalidateCache(skillSlotIndex);
        }
    }
}
