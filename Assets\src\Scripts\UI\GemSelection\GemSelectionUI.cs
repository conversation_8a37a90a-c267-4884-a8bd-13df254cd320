using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using Sirenix.OdinInspector;
using Flexalon;

public class GemSelectionUI : MonoBehaviour
{
    [Title("UI References")]
    [BoxGroup("Panel")]
    [Required]
    public GameObject selectionPanel;
    
    [BoxGroup("Panel")]
    [Required]
    public TextMeshProUGUI titleText;
    
    [BoxGroup("Cards")]
    [Required]
    public Transform cardContainer;
    
    [BoxGroup("Cards")]
    [Required]
    public GemSelectionCard cardPrefab;
    
    [BoxGroup("Cards")]
    [Required]
    [InfoBox("Optional: Different card prefab for buff selections")]
    public GemSelectionCard buffCardPrefab;
    
    [ShowInInspector]
    [ReadOnly]
    [BoxGroup("Runtime")]
    private List<GemSelectionCard> activeCards = new List<GemSelectionCard>();
    
    [ShowInInspector]
    [ReadOnly]
    [BoxGroup("Runtime")]
    private List<ISelectable> currentSelectables = new List<ISelectable>();

    [FoldoutGroup("Advanced")]
    [InfoBox("Flexalon object for dynamic title layout updates")]
    public FlexalonObject titleFlexalonObject;
    
    private void Start()
    {
        // Make sure panel is hidden at start
        if (selectionPanel != null)
        {
            selectionPanel.SetActive(false);
        }
    }
    
    public void ShowGemChoices(List<GemInstance> gems, bool isSkillGem)
    {
        // Convert to selectables
        currentSelectables.Clear();
        foreach (var gem in gems)
        {
            currentSelectables.Add(new SelectableGem(gem));
        }
        
        string title = isSkillGem ? "Choose a Skill Gem" : "Choose a Support Gem";
        ShowSelectionChoices(currentSelectables, title);
    }
    
    public void ShowBuffChoices(List<PlayerBuffData> buffs)
    {
        // Convert to selectables
        currentSelectables.Clear();
        foreach (var buff in buffs)
        {
            currentSelectables.Add(new SelectableBuff(buff));
        }
        
        ShowSelectionChoices(currentSelectables, "Choose a Buff");
    }
    
    public void ShowMixedChoices(List<ISelectable> selectables, string customTitle = null)
    {
        currentSelectables.Clear();
        currentSelectables.AddRange(selectables);
        
        ShowSelectionChoices(currentSelectables, customTitle ?? "Make Your Choice");
    }
    
    private void ShowSelectionChoices(List<ISelectable> selectables, string title)
    {
        // Clear any existing cards
        ClearCards();
        
        // Update title
        if (titleText != null)
        {
            titleText.text = title;
            // Force Flexalon to Update the Layout
            if (titleFlexalonObject != null)
            {
                titleFlexalonObject.ForceUpdate();
            }
        }
        
        // Show panel
        if (selectionPanel != null)
        {
            selectionPanel.SetActive(true);
        }
        
        // Create cards for each selectable
        for (int i = 0; i < selectables.Count; i++)
        {
            CreateCard(selectables[i], i);
        }
    }
    
    private void CreateCard(ISelectable selectable, int index)
    {
        if (cardContainer == null)
            return;
        
        // Choose appropriate card prefab
        GemSelectionCard prefabToUse = cardPrefab;
        if (selectable.GetSelectionType() == SelectionType.PlayerBuff && buffCardPrefab != null)
        {
            prefabToUse = buffCardPrefab;
        }
        
        if (prefabToUse == null)
            return;
        
        // Instantiate card
        var card = Instantiate(prefabToUse, cardContainer);
        
        if (card != null)
        {
            // Setup card with selectable data
            card.Setup(selectable, this);
            
            // Add to active cards list
            activeCards.Add(card);
            
            // No animation required; cards appear instantly
        }
    }
    
    public void OnSelectableChosen(ISelectable selectable)
    {
        // Handle selection based on type
        if (selectable is SelectableGem selectableGem)
        {
            // Notify the gem manager
            var manager = GemSelectionManager.Instance;
            if (manager != null)
            {
                manager.SelectGem(selectableGem.GetGem());
            }
        }
        else if (selectable is SelectableBuff selectableBuff)
        {
            // Notify the gem manager about buff selection
            var manager = GemSelectionManager.Instance;
            if (manager != null)
            {
                manager.SelectBuff(selectableBuff.GetBuff());
            }
        }
        
        // Clear selectables
        currentSelectables.Clear();
        
        // Immediately hide UI without animations
        Hide();
    }
    
    // Backward compatibility
    public void SelectGem(GemInstance selectedGem)
    {
        OnSelectableChosen(new SelectableGem(selectedGem));
    }
    
    public void Hide()
    {
        // Clear cards
        ClearCards();
        
        // Hide panel
        if (selectionPanel != null)
        {
            selectionPanel.SetActive(false);
        }
    }
    
    private void ClearCards()
    {
        foreach (var card in activeCards)
        {
            if (card != null)
            {
                Destroy(card.gameObject);
            }
        }
        activeCards.Clear();
    }

    // --------------------------------
    // Runtime debug helpers
    // --------------------------------

    // Pressing F4 opens a random mixed selection (skill gem, support gem, buff)
    private void Update()
    {
        if (Input.GetKeyDown(KeyCode.F4))
        {
            TriggerRandomMixedSelection();
        }
    }

    /// <summary>
    /// Shows a random mix of one skill gem, one support gem and one player buff.
    /// This mimics the behaviour of the inspector debug button but can be triggered at runtime via the F4 key.
    /// </summary>
    private void TriggerRandomMixedSelection()
    {
        var selectables = new List<ISelectable>(3);

        // Random skill gem
        var skillGems = Resources.FindObjectsOfTypeAll<SkillGemData>();
        if (skillGems.Length > 0)
        {
            var randomSkill = skillGems[Random.Range(0, skillGems.Length)];
            selectables.Add(new SelectableGem(new GemInstance(randomSkill)));
        }

        // Random player buff
        var buffs = Resources.FindObjectsOfTypeAll<PlayerBuffData>();
        if (buffs.Length > 0)
        {
            var randomBuff = buffs[Random.Range(0, buffs.Length)];
            selectables.Add(new SelectableBuff(randomBuff));
        }

        // Random support gem
        var supportGems = Resources.FindObjectsOfTypeAll<SupportGemData>();
        if (supportGems.Length > 0)
        {
            var randomSupport = supportGems[Random.Range(0, supportGems.Length)];
            selectables.Add(new SelectableGem(new GemInstance(randomSupport)));
        }

        if (selectables.Count > 0)
        {
            ShowMixedChoices(selectables, "Choose Your Upgrade");
        }
        else
        {
            Debug.LogWarning("GemSelectionUI: No items found for random mixed selection.");
        }
    }
    
    // Debug method
    [FoldoutGroup("Debug")]
    [Button("Test Show Skill Gems", ButtonSizes.Large)]
    [PropertySpace(10)]
    private void TestShowSkillGems()
    {
        var testGems = new List<GemInstance>();
        
        // Find some skill gems in the project
        var skillGems = Resources.FindObjectsOfTypeAll<SkillGemData>();
        for (int i = 0; i < Mathf.Min(3, skillGems.Length); i++)
        {
            testGems.Add(new GemInstance(skillGems[i]));
        }
        
        if (testGems.Count > 0)
        {
            ShowGemChoices(testGems, true);
        }
        else
        {
            UnityEngine.Debug.LogWarning("No skill gems found for testing");
        }
    }
    
    [FoldoutGroup("Debug")]
    [Button("Test Show Support Gems", ButtonSizes.Large)]
    [PropertySpace(10)]
    private void TestShowSupportGems()
    {
        var testGems = new List<GemInstance>();
        
        // Find some support gems in the project
        var supportGems = Resources.FindObjectsOfTypeAll<SupportGemData>();
        for (int i = 0; i < Mathf.Min(3, supportGems.Length); i++)
        {
            var instance = new GemInstance(supportGems[i]);
            testGems.Add(instance);
        }
        
        if (testGems.Count > 0)
        {
            ShowGemChoices(testGems, false);
        }
        else
        {
            UnityEngine.Debug.LogWarning("No support gems found for testing");
        }
    }
    
    [FoldoutGroup("Debug")]
    [Button("Test Show Buffs", ButtonSizes.Large)]
    [PropertySpace(10)]
    private void TestShowBuffs()
    {
        var testBuffs = new List<PlayerBuffData>();
        
        // Find some buffs in the project
        var buffs = Resources.FindObjectsOfTypeAll<PlayerBuffData>();
        for (int i = 0; i < Mathf.Min(3, buffs.Length); i++)
        {
            testBuffs.Add(buffs[i]);
        }
        
        if (testBuffs.Count > 0)
        {
            ShowBuffChoices(testBuffs);
        }
        else
        {
            UnityEngine.Debug.LogWarning("No buffs found for testing");
        }
    }
    
    [FoldoutGroup("Debug")]
    [Button("Test Show Mixed (Gems + Buffs)", ButtonSizes.Large)]
    [PropertySpace(10)]
    private void TestShowMixed()
    {
        var selectables = new List<ISelectable>();
        
        // Add a skill gem
        var skillGems = Resources.FindObjectsOfTypeAll<SkillGemData>();
        if (skillGems.Length > 0)
        {
            selectables.Add(new SelectableGem(new GemInstance(skillGems[0])));
        }
        
        // Add a buff
        var buffs = Resources.FindObjectsOfTypeAll<PlayerBuffData>();
        if (buffs.Length > 0)
        {
            selectables.Add(new SelectableBuff(buffs[0]));
        }
        
        // Add a support gem
        var supportGems = Resources.FindObjectsOfTypeAll<SupportGemData>();
        if (supportGems.Length > 0)
        {
            selectables.Add(new SelectableGem(new GemInstance(supportGems[0])));
        }
        
        if (selectables.Count > 0)
        {
            ShowMixedChoices(selectables, "Choose Your Upgrade");
        }
        else
        {
            UnityEngine.Debug.LogWarning("No items found for testing");
        }
    }
}